<!DOCTYPE html>
<html>
<head>
    <title>Admin Panel - Vote Results</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> <!-- Include jQuery -->
</head>
<body>
    <h1>Admin Panel - Vote Results</h1>
    <button id="showResults">Show Vote Results</button>
    <div id="resultsTable"></div>

    <script>
        // JavaScript to fetch and display results on button click
        $(document).ready(function () {
            $("#showResults").click(function () {
                $.ajax({
                    url: 'admin.php', // PHP script to fetch results
                    type: 'GET',
                    success: function (data) {
                        $('#resultsTable').html(data); // Display the results in a div
                    },
                    error: function () {
                        alert('Error fetching results.');
                    }
                });
            });
        });
    </script>
</body>
</html>
