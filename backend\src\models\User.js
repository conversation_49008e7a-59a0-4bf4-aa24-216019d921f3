import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import mongoose from 'mongoose'
import validator from 'validator'

const userSchema = new mongoose.Schema(
  {
    username: {
      type: String,
      required: [true, 'Username is required'],
      unique: true,
      trim: true,
      lowercase: true,
      minlength: [3, 'Username must be at least 3 characters'],
      maxlength: [50, 'Username cannot exceed 50 characters'],
    },
    municipality: {
      type: String,
      required: [true, 'Municipality is required'],
      trim: true,
      enum: {
        values: [
          '<PERSON><PERSON>',
          '<PERSON><PERSON><PERSON>',
          '<PERSON><PERSON>',
          '<PERSON><PERSON>',
          '<PERSON><PERSON><PERSON>',
          '<PERSON><PERSON>',
          'Cabusa<PERSON>',
          'Calabanga',
          'Camaligan',
          'Canaman',
          'Caramoan',
          'Del Gallego',
          'Gainza',
          'Garchitorena',
          'Goa',
          'Lagonoy',
          'Libmanan',
          'Lupi',
          'Magarao',
          'Milaor',
          'Minalabac',
          'Nabua',
          'Ocampo',
          '<PERSON><PERSON><PERSON><PERSON>',
          'Pasa<PERSON><PERSON>',
          '<PERSON><PERSON><PERSON><PERSON>',
          '<PERSON><PERSON>',
          '<PERSON><PERSON><PERSON>',
          '<PERSON><PERSON><PERSON>',
          'San Fernando',
          'San Jose',
          '<PERSON><PERSON><PERSON>',
          'Sir<PERSON>',
          'Tigaon',
          'Tinambac',
        ],
        message: 'Invalid municipality name',
      },
    },
    district: {
      type: String,
      required: function () {
        return this.role !== 'admin'
      },
      enum: {
        values: ['1st District', '2nd District', '3rd District', '4th District', '5th District'],
        message:
          'Invalid district. Must be one of: 1st District, 2nd District, 3rd District, 4th District, 5th District',
      },
    },
    password: {
      type: String,
      required: [true, 'Password is required'],
      minlength: [6, 'Password must be at least 6 characters'],
      select: false, // Don't include password in queries by default
    },
    role: {
      type: String,
      enum: {
        values: ['voter', 'admin'],
        message: 'Invalid role. Only voter and admin roles are allowed.',
      },
      default: 'voter',
    },
    email: {
      type: String,
      sparse: true, // Allow multiple null values
      validate: {
        validator: function (email) {
          return !email || validator.isEmail(email)
        },
        message: 'Please provide a valid email address',
      },
      lowercase: true,
      trim: true,
      // Only required for admin role
      required: function () {
        return this.role === 'admin'
      },
    },
    hasVoted: {
      type: Boolean,
      default: false,
    },
    lastVotedAt: {
      type: Date,
      default: null,
    },
    lastLogin: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPresent: {
      type: Boolean,
      default: true,
      index: true,
    },
    refreshTokens: [
      {
        token: {
          type: String,
          required: true,
        },
        createdAt: {
          type: Date,
          default: Date.now,
          expires: 604800, // 7 days
        },
      },
    ],
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.password
        delete ret.refreshTokens
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes for performance
userSchema.index({ municipality: 1 })
userSchema.index({ district: 1 })
userSchema.index({ role: 1 })
userSchema.index({ hasVoted: 1 })

// Virtual for account lock status
userSchema.virtual('isLocked').get(function () {
  return !!(this.lockUntil && this.lockUntil > Date.now())
})

// Pre-save middleware to hash password
userSchema.pre('save', async function (next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next()

  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error)
  }
})

// Instance method to check password
userSchema.methods.comparePassword = async function (candidatePassword) {
  if (!this.password) return false
  return await bcrypt.compare(candidatePassword, this.password)
}

// Instance method to generate JWT token
userSchema.methods.generateAuthToken = function () {
  const payload = {
    id: this._id,
    username: this.username,
    municipality: this.municipality,
    role: this.role,
  }

  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '15m',
  })
}

// Instance method to generate refresh token
userSchema.methods.generateRefreshToken = function () {
  const payload = {
    id: this._id,
    type: 'refresh',
  }

  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d',
  })
}

// Instance method to handle failed login attempts
userSchema.methods.incLoginAttempts = function () {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    })
  }

  const updates = { $inc: { loginAttempts: 1 } }

  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 } // 2 hours
  }

  return this.updateOne(updates)
}

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function () {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLogin: new Date() },
  })
}

// Static method to find user for authentication
userSchema.statics.findByCredentials = async function (username, password) {
  const user = await this.findOne({
    username: username.toLowerCase(),
    isActive: true,
  }).select('+password')

  if (!user) {
    throw new Error('Invalid login credentials')
  }

  if (user.isLocked) {
    throw new Error('Account temporarily locked due to too many failed login attempts')
  }

  const isMatch = await user.comparePassword(password)
  if (!isMatch) {
    await user.incLoginAttempts()
    throw new Error('Invalid login credentials')
  }

  // Reset login attempts on successful login
  await user.resetLoginAttempts()
  return user
}

// Static method to check account limits
userSchema.statics.checkAccountLimits = async function (role) {
  const MAX_VOTERS = 35
  const MAX_ADMINS = 1

  if (role === 'voter') {
    const voterCount = await this.countDocuments({ role: 'voter', isActive: true })
    if (voterCount >= MAX_VOTERS) {
      throw new Error(`Maximum number of voters (${MAX_VOTERS}) has been reached`)
    }
  } else if (role === 'admin') {
    const adminCount = await this.countDocuments({ role: 'admin', isActive: true })
    if (adminCount >= MAX_ADMINS) {
      throw new Error(`Maximum number of admins (${MAX_ADMINS}) has been reached`)
    }
  }

  return true
}

// Static method to get account statistics
userSchema.statics.getAccountStats = async function () {
  const stats = await this.aggregate([
    {
      $match: { isActive: true },
    },
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 },
      },
    },
  ])

  const voterCount = stats.find(s => s._id === 'voter')?.count || 0
  const adminCount = stats.find(s => s._id === 'admin')?.count || 0

  return {
    voters: {
      current: voterCount,
      maximum: 35,
      remaining: Math.max(0, 35 - voterCount),
    },
    admins: {
      current: adminCount,
      maximum: 1,
      remaining: Math.max(0, 1 - adminCount),
    },
    total: voterCount + adminCount,
  }
}

// Static method to create default admin user
userSchema.statics.createDefaultAdmin = async function () {
  const adminExists = await this.findOne({ role: 'admin' })
  if (adminExists) return adminExists

  const admin = new this({
    username: 'admin',
    municipality: 'admin', // Admin user municipality
    password: process.env.ADMIN_DEFAULT_PASSWORD || 'socmob123',
    role: 'admin',
    email: process.env.ADMIN_EMAIL,
  })

  return await admin.save()
}

// District mapping configuration
const DISTRICT_MAPPING = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lupi', 'Ragay', 'Sipocot'],
  '2nd District': [
    'Gainza',
    'Libmanan',
    'Milaor',
    'Minalabac',
    'Pamplona',
    'Pasacao',
    'San Fernando',
  ],
  '3rd District': ['Bombon', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': [
    'Caramoan',
    'Garchitorena',
    'Goa',
    'Lagonoy',
    'Parubcan',
    'Sagnay',
    'San Jose',
    'Siruma',
    'Tigaon',
    'Tinambac',
  ],
  '5th District': ['Baao', 'Balatan', 'Bato', 'Buhi', 'Bula', 'Nabua'],
}

// Static method to get district for a municipality
userSchema.statics.getDistrictForMunicipality = function (municipality) {
  for (const [district, municipalities] of Object.entries(DISTRICT_MAPPING)) {
    if (municipalities.includes(municipality)) {
      return district
    }
  }
  throw new Error(`Municipality ${municipality} not found in any district`)
}

// Static method to get municipalities in a district
userSchema.statics.getMunicipalitiesInDistrict = function (district) {
  return DISTRICT_MAPPING[district] || []
}

// Static method to get users by district
userSchema.statics.getUsersByDistrict = function (district) {
  return this.find({ district, isActive: true }).select('-password -refreshTokens')
}

// Static method to get voting statistics by district
userSchema.statics.getVotingStatsByDistrict = function () {
  return this.aggregate([
    {
      $match: { role: 'voter', isActive: true },
    },
    {
      $group: {
        _id: '$district',
        totalVoters: { $sum: 1 },
        votedCount: {
          $sum: {
            $cond: [{ $eq: ['$hasVoted', true] }, 1, 0],
          },
        },
        municipalities: { $addToSet: '$municipality' },
      },
    },
    {
      $project: {
        district: '$_id',
        totalVoters: 1,
        votedCount: 1,
        pendingVoters: { $subtract: ['$totalVoters', '$votedCount'] },
        participationRate: {
          $multiply: [{ $divide: ['$votedCount', '$totalVoters'] }, 100],
        },
        municipalities: 1,
        municipalityCount: { $size: '$municipalities' },
      },
    },
    {
      $sort: { district: 1 },
    },
  ])
}

// Static method to generate secure password
userSchema.statics.generateSecurePassword = function (municipality) {
  // Generate 8-character password: 4 letters + 1 symbol + 3 numbers
  // Format: municipalityPrefix + symbol + randomNumbers
  const municipalityPrefix = municipality.toLowerCase().substring(0, 4).padEnd(4, 'x')
  const symbols = ['#', '@', '$', '%', '&', '*', '+', '=']
  const symbol = symbols[Math.floor(Math.random() * symbols.length)]

  // Generate 3 random numbers (avoiding confusing characters)
  const numbers = []
  for (let i = 0; i < 3; i++) {
    numbers.push(Math.floor(Math.random() * 10))
  }

  return `${municipalityPrefix}${symbol}${numbers.join('')}`
}

const User = mongoose.model('User', userSchema)

export default User
