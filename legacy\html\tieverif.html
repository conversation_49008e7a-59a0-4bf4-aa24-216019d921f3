<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tie Verification</title>
    <!-- Uncomment the line below if you have a separate CSS file -->
    <!-- <link rel="stylesheet" type="text/css" href="style.css"> -->
</head>
<body>
    <header>
        <h1>Tie Verification</h1>
    </header>

    <section>
        <?php if ($isTie): ?>
            <div class="message">
                <p>There is a tie. The following candidates share the same vote value for the 15th position:</p>
            </div>
            <table>
                <tr>
                    <th>No</th>
                    <th>Candidate Name</th>
                    <th>Total Votes</th>
                </tr>
                <?php foreach ($tiedCandidates as $count => $candidate): ?>
                    <tr>
                        <td><?= $count + 1 ?></td>
                        <td><?= $candidate['candidate_name'] ?></td>
                        <td><?= $candidate['total_votes'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php else: ?>
            <div class="message">
                <p>There is no tie. The top 15 most voted candidates can now log in <a href="execom_login.html">here</a>:</p>
            </div>
            <table>
                <tr>
                    <th>No</th>
                    <th>Candidate Name</th>
                    <th>Total Votes</th>
                </tr>
                <?php foreach (array_slice($candidates, 0, 15) as $count => $candidate): ?>
                    <tr>
                        <td><?= $count + 1 ?></td>
                        <td><?= $candidate['candidate_name'] ?></td>
                        <td><?= $candidate['total_votes'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </section>

    <footer>
        <div class="footer-row">
            <p>&copy; made by gien</p>
        </div>
    </footer>
</body>
</html>
