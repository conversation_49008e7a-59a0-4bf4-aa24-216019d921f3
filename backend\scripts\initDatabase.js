#!/usr/bin/env node

import dotenv from 'dotenv'
import mongoose from 'mongoose'
import User from '../src/models/User.js'
import logger from '../src/utils/logger.js'

// Load environment variables
dotenv.config()

// District mapping for municipalities
const DISTRICT_MAPPING = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lu<PERSON>', 'Ra<PERSON>y', 'Sipocot'],
  '2nd District': [
    'Gainza',
    'Lib<PERSON>n',
    'Milaor',
    'Minalabac',
    'Pamplona',
    'Pasacao',
    'San Fernando',
  ],
  '3rd District': ['Bombon', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': [
    'Caramoan',
    'Garchitorena',
    'Goa',
    'Lagonoy',
    'Parubcan',
    'Sagnay',
    'San Jose',
    'Siruma',
    'Tigaon',
    'Tinambac',
  ],
  '5th District': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
}

// Flatten municipalities list
const MUNICIPALITIES = Object.values(DISTRICT_MAPPING).flat()

// Function to get district for municipality
function getDistrictForMunicipality(municipality) {
  for (const [district, municipalities] of Object.entries(DISTRICT_MAPPING)) {
    if (municipalities.includes(municipality)) {
      return district
    }
  }
  throw new Error(`Municipality ${municipality} not found in any district`)
}

async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/dfpta_db'
    await mongoose.connect(mongoURI)
    logger.info(`Connected to MongoDB: ${mongoURI}`)
  } catch (error) {
    logger.error('MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

async function createDefaultAdmin() {
  try {
    const existingAdmin = await User.findOne({ role: 'admin' })
    if (existingAdmin) {
      logger.info('Admin user already exists')
      return existingAdmin
    }

    const admin = new User({
      username: 'admin',
      municipality: 'Pili', // Capital of Camarines Sur
      district: getDistrictForMunicipality('Pili'),
      password: process.env.ADMIN_DEFAULT_PASSWORD || 'socmob123',
      role: 'admin',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      isActive: true,
    })

    await admin.save()
    logger.info('Default admin user created successfully')
    logger.info(`Admin credentials: admin / ${process.env.ADMIN_DEFAULT_PASSWORD || 'socmob123'}`)
    return admin
  } catch (error) {
    logger.error('Error creating admin user:', error.message)
    logger.error('Full error:', error)
    throw error
  }
}

async function createMunicipalityUsers() {
  try {
    const existingUsers = await User.find({ role: 'voter' })
    const existingMunicipalities = existingUsers.map(user => user.municipality)

    const usersToCreate = []

    for (const municipality of MUNICIPALITIES) {
      if (!existingMunicipalities.includes(municipality)) {
        const district = getDistrictForMunicipality(municipality)
        // Generate secure password manually since static method might not be available
        const municipalityPrefix = municipality.toLowerCase().substring(0, 4).padEnd(4, 'x')
        const symbols = ['#', '@', '$', '%', '&', '*', '+', '=']
        const symbol = symbols[Math.floor(Math.random() * symbols.length)]
        const numbers = []
        for (let i = 0; i < 3; i++) {
          numbers.push(Math.floor(Math.random() * 10))
        }
        const securePassword = `${municipalityPrefix}${symbol}${numbers.join('')}`

        usersToCreate.push({
          username: municipality.toLowerCase().replace(/\s+/g, ''),
          municipality: municipality,
          district: district,
          password: securePassword,
          role: 'voter',
          isActive: true,
        })
      }
    }

    if (usersToCreate.length > 0) {
      await User.insertMany(usersToCreate)
      logger.info(`Created ${usersToCreate.length} municipality users`)

      // Log the created users for reference
      usersToCreate.forEach(user => {
        logger.info(`Created user: ${user.username} / ${user.password} (${user.municipality})`)
      })
    } else {
      logger.info('All municipality users already exist')
    }

    return usersToCreate.length
  } catch (error) {
    logger.error('Error creating municipality users:', error.message)
    throw error
  }
}

async function createSampleExecomUsers() {
  try {
    const existingExecom = await User.find({ role: 'execom' })
    if (existingExecom.length > 0) {
      logger.info('Executive committee users already exist')
      return
    }

    // Create a few sample execom users from different municipalities
    const execomUsers = [
      {
        username: 'execom_pili',
        municipality: 'Pili',
        district: getDistrictForMunicipality('Pili'),
        password: 'pili#123',
        role: 'execom',
        email: '<EMAIL>',
        isActive: true,
      },
      {
        username: 'execom_nabua',
        municipality: 'Nabua',
        district: getDistrictForMunicipality('Nabua'),
        password: 'nabu@456',
        role: 'execom',
        email: '<EMAIL>',
        isActive: true,
      },
    ]

    await User.insertMany(execomUsers)
    logger.info(`Created ${execomUsers.length} executive committee users`)

    execomUsers.forEach(user => {
      logger.info(`Created execom user: ${user.username} / ${user.password} (${user.municipality})`)
    })
  } catch (error) {
    logger.error('Error creating execom users:', error.message)
    throw error
  }
}

async function createTieBreakerUser() {
  try {
    const existingTieBreaker = await User.findOne({ role: 'tie-breaker' })
    if (existingTieBreaker) {
      logger.info('Tie-breaker user already exists')
      return
    }

    const tieBreaker = new User({
      username: 'tiebreaker',
      municipality: 'Pili', // Central location
      district: getDistrictForMunicipality('Pili'),
      password: 'pili$789',
      role: 'tie-breaker',
      email: '<EMAIL>',
      isActive: true,
    })

    await tieBreaker.save()
    logger.info('Tie-breaker user created successfully')
    logger.info(`Tie-breaker credentials: tiebreaker / tiebreaker123`)
  } catch (error) {
    logger.error('Error creating tie-breaker user:', error.message)
    throw error
  }
}

async function displaySummary() {
  try {
    const stats = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
        },
      },
    ])

    logger.info('\n=== DATABASE INITIALIZATION SUMMARY ===')
    stats.forEach(stat => {
      logger.info(`${stat._id}: ${stat.count} users`)
    })

    const totalUsers = await User.countDocuments()
    logger.info(`Total users: ${totalUsers}`)
    logger.info('========================================\n')
  } catch (error) {
    logger.error('Error generating summary:', error.message)
  }
}

async function main() {
  try {
    logger.info('Starting database initialization...')

    // Connect to database
    await connectDB()

    // Create default admin
    await createDefaultAdmin()

    // Create municipality users (voters)
    await createMunicipalityUsers()

    // Create sample executive committee users
    await createSampleExecomUsers()

    // Create tie-breaker user
    await createTieBreakerUser()

    // Display summary
    await displaySummary()

    logger.info('Database initialization completed successfully!')
  } catch (error) {
    logger.error('Database initialization failed:', error.message)
    process.exit(1)
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Run the initialization
main()
