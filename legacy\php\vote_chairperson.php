<?php
include 'db_connection.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if the form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate the form data
    if (!isset($_POST['candidate_id']) || !is_array($_POST['candidate_id'])) {
        die('Invalid form submission');
    }

    // Assuming you have a voter_id (you might need to implement authentication)
    $voter_id = 1; // Change this based on your authentication mechanism

    // Prepare and execute SQL statements to insert vote
    $stmt = $conn->prepare("INSERT INTO chairperson (voter_id, candidate_id) VALUES (?, ?)");
    $stmt->bind_param('ii', $voter_id, $candidate_id);

    foreach ($_POST['candidate_id'] as $candidate_id) {
        $stmt->execute();
    }

    // Close the prepared statement
    $stmt->close();

    // Close the database connection
    $conn->close();

    // Redirect the user or display a success message
    header("Location: vote_success.php"); // Redirect to a success page
    exit();
}
?>
