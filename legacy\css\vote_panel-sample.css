body {
    font-family: 'Urbanist', sans-serif;
    background-color: #f0f0f0;
    text-align: center;
    margin: 0;
    padding: 0;
}
.header {
    background: #fff;
    color: #333;
    padding: 0;
    text-align: center;
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.container-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 100px;
    margin: 0;
}

.logo {
    width: 50px; 
    margin-right: 10px;
}

.logo-container b {
    font-family: 'Urbanist', sans-serif;
    align-items: center;
    font-size: 16px; 
    font-weight: bold;
    color: #243328;
}
form {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    margin: 40px;
    text-align: center; /* Center the text horizontally */
} 
.form-section {
    background-image: url('ff/bgg1.png');
    background-size: cover; 
    background-repeat: no-repeat;
    background-position: center; 
    flex: 1;
    padding: 0; 
    text-align: center;
    margin-top: 0;
}

/* Navigation styles */
.nav-center {
    display: flex;
    gap: 30px;
    font-size: 14px;
}

.nav-button {
    font-family: 'Urbanist', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #243328;
    text-transform: uppercase;
    text-decoration: none;
    letter-spacing: 0.15em;
    padding: 15px 20px;
    position: relative;
    transition: background 0.3s;
}

.nav-button:hover {
    background: #f0f0f0; 
}

a.nav-button {
    color: #243328;
}

a {
    text-decoration: none;
    color: inherit;
}

.candidates {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 5px;
    margin: 10px auto;
    align-items: flex-start;
    justify-content: start;
}

input[type="checkbox"] {
    margin-bottom: 5px;
    margin-right: 5px; 
}


.candidates > div {
    font-family: 'Urbanist', sans-serif;
    display: flex;
    align-items: center;
    padding: 5px;
    border: none;
    text-align: left; 
}

.form-container {
    justify-content: right;
    display: flex;
    flex-direction: column; 
    align-items: center; 
    height: 100vh; 
}

.vote-button {
    margin-top: 20px; 
}

.selected-candidates {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.selected-candidates h2 {
    background-color: #243328;
    color: white;
    padding: 10px;
    margin: 0;
    border-radius: 10px 10px 0 0;
}

.highlight {
    background-color: #ffffcc; 
    font-weight: bold;
}

ul {
    list-style-type: none; 
    padding: 0;
    margin: 0; 
    display: grid; 
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(5, auto); 
    gap: 10px;
}

ul li {
    background-color: #f0f0f0; 
    padding: 10px;
    border-radius: 5px; 
    text-align: center; 
    font-size: 1.1rem; 
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
}
.logout-button {
    background-color: #26704C;
    color: #fff;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 30px; 
}
.logout-button:hover {
    background-color: #E98E53;
}