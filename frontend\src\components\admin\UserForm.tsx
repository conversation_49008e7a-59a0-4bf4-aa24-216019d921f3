import { Button } from '@/components/ui/button'
import {
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useDistrictsAndMunicipalities } from '@/hooks/useSystem'
import { User } from '@/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

// Create dynamic schema function
const createUserSchema = (municipalities: string[], districts: string[]) =>
  z.object({
    username: z
      .string()
      .min(3, 'Username must be at least 3 characters')
      .max(50, 'Username cannot exceed 50 characters')
      .regex(
        /^[a-zA-Z0-9_-]+$/,
        'Username can only contain letters, numbers, underscores, and hyphens'
      ),
    municipality: z.enum(municipalities as [string, ...string[]], {
      required_error: 'Please select a municipality',
    }),
    district: z.enum(districts as [string, ...string[]], {
      required_error: 'Please select a district',
    }),
    password: z
      .string()
      .min(6, 'Password must be at least 6 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one lowercase letter, one uppercase letter, and one number'
      )
      .optional(),
    role: z.enum(['voter', 'admin', 'execom', 'tie-breaker'], {
      required_error: 'Please select a role',
    }),
    email: z.string().email('Please enter a valid email').optional().or(z.literal('')),
    isActive: z.boolean().default(true),
  })

// Default schema for fallback
const defaultUserSchema = createUserSchema([''], [''])
type UserFormData = z.infer<typeof defaultUserSchema>

interface UserFormProps {
  user?: User | null
  onSubmit: (data: UserFormData) => void
  onCancel: () => void
  isLoading: boolean
}

export function UserForm({ user, onSubmit, onCancel, isLoading }: UserFormProps) {
  const isEditing = !!user

  // Get dynamic system data
  const { data: systemData, isLoading: isSystemLoading } = useDistrictsAndMunicipalities()

  // Create dynamic schema based on available data
  const userSchema = systemData
    ? createUserSchema(systemData.municipalities, systemData.districts)
    : defaultUserSchema

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      username: user?.username || '',
      municipality: user?.municipality || undefined,
      district: user?.district || undefined,
      password: '',
      role: user?.role || 'voter',
      email: user?.email || '',
      isActive: user?.isActive ?? true,
    },
  })

  const watchedRole = watch('role')
  const watchedMunicipality = watch('municipality')
  const watchedIsActive = watch('isActive')

  // Auto-populate district when municipality changes
  useEffect(() => {
    if (watchedMunicipality) {
      try {
        // Manual district mapping since import isn't working
        const districtMapping: Record<string, string> = {
          Cabusao: '1st District',
          'Del Gallego': '1st District',
          Lupi: '1st District',
          Ragay: '1st District',
          Sipocot: '1st District',
          Gainza: '2nd District',
          Libmanan: '2nd District',
          Milaor: '2nd District',
          Minalabac: '2nd District',
          Pamplona: '2nd District',
          Pasacao: '2nd District',
          'San Fernando': '2nd District',
          Bombon: '3rd District',
          Calabanga: '3rd District',
          Camaligan: '3rd District',
          Canaman: '3rd District',
          Magarao: '3rd District',
          Ocampo: '3rd District',
          Pili: '3rd District',
          Caramoan: '4th District',
          Garchitorena: '4th District',
          Goa: '4th District',
          Lagonoy: '4th District',
          Parubcan: '4th District',
          Sagnay: '4th District',
          'San Jose': '4th District',
          Siruma: '4th District',
          Tigaon: '4th District',
          Tinambac: '4th District',
          Baao: '5th District',
          Balatan: '5th District',
          Bato: '5th District',
          Buhi: '5th District',
          Bula: '5th District',
          Nabua: '5th District',
        }

        const district = districtMapping[watchedMunicipality]
        if (district) {
          setValue('district', district as any)
        }
      } catch (error) {
        console.warn('Could not auto-populate district:', error)
      }
    }
  }, [watchedMunicipality, setValue])

  const handleFormSubmit = (data: UserFormData) => {
    // Remove password if editing and password is empty
    if (isEditing && !data.password) {
      const { password, ...dataWithoutPassword } = data
      onSubmit(dataWithoutPassword as UserFormData)
    } else {
      onSubmit(data)
    }
  }

  return (
    <DialogContent className='sm:max-w-[425px]'>
      <DialogHeader>
        <DialogTitle>{isEditing ? 'Edit User' : 'Add New User'}</DialogTitle>
        <DialogDescription>
          {isEditing
            ? 'Update user information. Leave password empty to keep current password.'
            : 'Create a new user account for the voting system.'}
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit(handleFormSubmit)} className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='username'>Username</Label>
          <Input
            id='username'
            {...register('username')}
            placeholder='Enter username'
            disabled={isEditing} // Don't allow username changes
          />
          {errors.username && <p className='text-sm text-red-600'>{errors.username.message}</p>}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='municipality'>Municipality</Label>
          <Select
            value={watchedMunicipality}
            onValueChange={value => setValue('municipality', value as any)}
          >
            <SelectTrigger>
              <SelectValue placeholder='Select municipality' />
            </SelectTrigger>
            <SelectContent>
              {systemData?.municipalities.map((municipality: string) => (
                <SelectItem key={municipality} value={municipality}>
                  {municipality}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.municipality && (
            <p className='text-sm text-red-600'>{errors.municipality.message}</p>
          )}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='district'>District</Label>
          <Select
            value={watch('district')}
            onValueChange={value => setValue('district', value as any)}
          >
            <SelectTrigger>
              <SelectValue placeholder='Select district' />
            </SelectTrigger>
            <SelectContent>
              {systemData?.districts.map((district: string) => (
                <SelectItem key={district} value={district}>
                  {district}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.district && <p className='text-sm text-red-600'>{errors.district.message}</p>}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='password'>Password {isEditing && '(leave empty to keep current)'}</Label>
          <Input
            id='password'
            type='password'
            {...register('password')}
            placeholder={isEditing ? 'Leave empty to keep current' : 'Enter password'}
          />
          {errors.password && <p className='text-sm text-red-600'>{errors.password.message}</p>}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='role'>Role</Label>
          <Select value={watchedRole} onValueChange={value => setValue('role', value as any)}>
            <SelectTrigger>
              <SelectValue placeholder='Select role' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='voter'>Voter</SelectItem>
              <SelectItem value='admin'>Admin</SelectItem>
              <SelectItem value='execom'>Executive Committee</SelectItem>
              <SelectItem value='tie-breaker'>Tie-breaker</SelectItem>
            </SelectContent>
          </Select>
          {errors.role && <p className='text-sm text-red-600'>{errors.role.message}</p>}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='email'>Email (optional)</Label>
          <Input id='email' type='email' {...register('email')} placeholder='Enter email address' />
          {errors.email && <p className='text-sm text-red-600'>{errors.email.message}</p>}
        </div>

        {isEditing && (
          <div className='flex items-center space-x-2'>
            <input
              type='checkbox'
              id='isActive'
              checked={watchedIsActive}
              onChange={e => setValue('isActive', e.target.checked)}
              className='rounded border-gray-300'
            />
            <Label htmlFor='isActive'>Active user</Label>
          </div>
        )}

        <DialogFooter>
          <Button type='button' variant='outline' onClick={onCancel}>
            Cancel
          </Button>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? 'Saving...' : isEditing ? 'Update User' : 'Create User'}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  )
}
