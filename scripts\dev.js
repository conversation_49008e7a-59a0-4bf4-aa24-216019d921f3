#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🚀 Starting DFPTA E-Voting System Development Servers...\n');

// Function to spawn a process with colored output
function spawnWithColor(command, args, cwd, color) {
  const process = spawn(command, args, {
    cwd,
    stdio: 'pipe',
    shell: true
  });

  const colorCodes = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };

  const prefix = color === 'blue' ? '[BACKEND]' : '[FRONTEND]';
  const colorCode = colorCodes[color] || '';
  const resetCode = colorCodes.reset;

  process.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log(`${colorCode}${prefix}${resetCode} ${line}`);
    });
  });

  process.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log(`${colorCode}${prefix}${resetCode} ${line}`);
    });
  });

  process.on('close', (code) => {
    console.log(`${colorCode}${prefix}${resetCode} Process exited with code ${code}`);
  });

  return process;
}

// Start backend server
console.log('📦 Starting backend server...');
const backendProcess = spawnWithColor('npm', ['run', 'dev'], join(projectRoot, 'backend'), 'blue');

// Start frontend server
console.log('⚛️  Starting frontend server...');
const frontendProcess = spawnWithColor('npm', ['run', 'dev'], join(projectRoot, 'frontend'), 'green');

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down servers...');
  backendProcess.kill('SIGINT');
  frontendProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down servers...');
  backendProcess.kill('SIGTERM');
  frontendProcess.kill('SIGTERM');
  process.exit(0);
});

console.log('\n✅ Development servers started!');
console.log('📍 Backend:  http://localhost:5000');
console.log('📍 Frontend: http://localhost:3000');
console.log('📍 API Docs: http://localhost:5000/api/docs');
console.log('\n💡 Press Ctrl+C to stop both servers\n');
