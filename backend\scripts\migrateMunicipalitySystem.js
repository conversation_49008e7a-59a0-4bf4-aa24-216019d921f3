#!/usr/bin/env node

import mongoose from 'mongoose'
import dotenv from 'dotenv'
import User from '../src/models/User.js'
import Candidate from '../src/models/Candidate.js'
import logger from '../src/utils/logger.js'

// Load environment variables
dotenv.config()

// District mapping
const DISTRICT_MAPPING = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lupi', 'Ragay', 'Sipocot'],
  '2nd District': ['Gainza', 'Libmanan', 'Milaor', 'Minalabac', 'Pamplona', 'Pasacao', 'San Fernando'],
  '3rd District': ['Bombon', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': ['Caramoan', 'Garchitorena', 'Goa', 'Lagonoy', 'Parubcan', 'Sa<PERSON><PERSON>', 'San Jose', 'Siruma', '<PERSON><PERSON><PERSON>', 'Tinamba<PERSON>'],
  '5th District': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>']
}

// Function to get district for municipality
function getDistrictForMunicipality(municipality) {
  for (const [district, municipalities] of Object.entries(DISTRICT_MAPPING)) {
    if (municipalities.includes(municipality)) {
      return district
    }
  }
  throw new Error(`Municipality ${municipality} not found in any district`)
}

async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/dfpta_voting'
    await mongoose.connect(mongoURI)
    logger.info(`Connected to MongoDB: ${mongoURI}`)
  } catch (error) {
    logger.error('MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

async function migrateCandidatesToMunicipalitySystem() {
  try {
    logger.info('Starting candidate migration to municipality-only system...')

    // Remove all existing candidates with personal information
    const deletedCandidates = await Candidate.deleteMany({})
    logger.info(`Removed ${deletedCandidates.deletedCount} existing candidates with personal information`)

    // Create municipality-based candidates
    const municipalityCandidates = []
    const allMunicipalities = Object.values(DISTRICT_MAPPING).flat()

    for (const municipality of allMunicipalities) {
      const district = getDistrictForMunicipality(municipality)
      municipalityCandidates.push({
        municipalityName: municipality,
        district: district,
        totalVotes: 0,
        isActive: true,
        votesByRound: [],
        currentRank: null,
        finalPosition: null,
        isWinner: false,
        isEliminated: false
      })
    }

    // Insert new municipality candidates
    const insertedCandidates = await Candidate.insertMany(municipalityCandidates)
    logger.info(`Created ${insertedCandidates.length} municipality-based candidates`)

    // Log summary by district
    const districtSummary = await Candidate.aggregate([
      {
        $group: {
          _id: '$district',
          count: { $sum: 1 },
          municipalities: { $push: '$municipalityName' }
        }
      },
      { $sort: { _id: 1 } }
    ])

    logger.info('\n=== CANDIDATE MIGRATION SUMMARY ===')
    districtSummary.forEach(district => {
      logger.info(`${district._id}: ${district.count} municipalities`)
      logger.info(`  Municipalities: ${district.municipalities.sort().join(', ')}`)
    })
    logger.info('===================================\n')

    return insertedCandidates

  } catch (error) {
    logger.error('Candidate migration failed:', error.message)
    throw error
  }
}

async function cleanupUserPersonalData() {
  try {
    logger.info('Cleaning up user personal data...')

    // Remove email from voter accounts (keep for admin/execom)
    const voterEmailCleanup = await User.updateMany(
      { role: 'voter' },
      { $unset: { email: 1 } }
    )
    logger.info(`Removed email from ${voterEmailCleanup.modifiedCount} voter accounts`)

    // Ensure all users have district information
    const usersWithoutDistrict = await User.find({
      $or: [
        { district: { $exists: false } },
        { district: null },
        { district: '' }
      ]
    })

    if (usersWithoutDistrict.length > 0) {
      logger.info(`Found ${usersWithoutDistrict.length} users without district information`)
      
      for (const user of usersWithoutDistrict) {
        try {
          const district = getDistrictForMunicipality(user.municipality)
          await User.updateOne(
            { _id: user._id },
            { $set: { district: district } }
          )
          logger.info(`Updated user ${user.username} with district: ${district}`)
        } catch (error) {
          logger.error(`Error updating user ${user.username}: ${error.message}`)
        }
      }
    }

    return true

  } catch (error) {
    logger.error('User data cleanup failed:', error.message)
    throw error
  }
}

async function validateMigration() {
  try {
    logger.info('Validating migration...')

    // Check candidates
    const candidateCount = await Candidate.countDocuments({ isActive: true })
    const expectedCandidateCount = Object.values(DISTRICT_MAPPING).flat().length
    
    if (candidateCount !== expectedCandidateCount) {
      throw new Error(`Expected ${expectedCandidateCount} candidates, found ${candidateCount}`)
    }

    // Check for any candidates with personal information
    const candidatesWithPersonalInfo = await Candidate.find({
      $or: [
        { name: { $exists: true } },
        { description: { $exists: true } },
        { contactInfo: { $exists: true } },
        { qualifications: { $exists: true } },
        { experience: { $exists: true } },
        { platform: { $exists: true } }
      ]
    })

    if (candidatesWithPersonalInfo.length > 0) {
      throw new Error(`Found ${candidatesWithPersonalInfo.length} candidates with personal information`)
    }

    // Check users
    const userCount = await User.countDocuments({ isActive: true })
    const usersWithoutDistrict = await User.countDocuments({
      $or: [
        { district: { $exists: false } },
        { district: null },
        { district: '' }
      ]
    })

    if (usersWithoutDistrict > 0) {
      throw new Error(`Found ${usersWithoutDistrict} users without district information`)
    }

    // Check voter emails are removed
    const votersWithEmail = await User.countDocuments({
      role: 'voter',
      email: { $exists: true, $ne: null, $ne: '' }
    })

    if (votersWithEmail > 0) {
      logger.warn(`Found ${votersWithEmail} voter accounts still with email addresses`)
    }

    logger.info('✅ Migration validation successful!')
    logger.info(`Total candidates: ${candidateCount}`)
    logger.info(`Total users: ${userCount}`)
    logger.info(`Users without district: ${usersWithoutDistrict}`)
    logger.info(`Voters with email: ${votersWithEmail}`)

    return true

  } catch (error) {
    logger.error('Migration validation failed:', error.message)
    throw error
  }
}

async function main() {
  try {
    logger.info('Starting municipality-based system migration...')
    
    await connectDB()
    await migrateCandidatesToMunicipalitySystem()
    await cleanupUserPersonalData()
    await validateMigration()
    
    logger.info('Municipality-based system migration completed successfully!')
    
  } catch (error) {
    logger.error('Migration failed:', error.message)
    process.exit(1)
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Run the migration
main()
