<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the database connection file
include 'db_connection.php';

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Get the user ID from the session
    $voterID = $_SESSION['user_id'];

    // Check if the user has already voted
    $sqlCheckVote = "SELECT * FROM tie_votes WHERE voter_id = '$voterID'";
    $resultCheckVote = $conn->query($sqlCheckVote);

    if ($resultCheckVote && $resultCheckVote->num_rows > 0) {
        // User has already voted, redirect to already_voted.php
        header('Location: already_voted.php');
        exit;
    } else {
        // User hasn't voted yet, proceed to process the vote

        if (isset($_POST['selected_candidates']) && is_array($_POST['selected_candidates'])) {
            // Get the selected candidates from the submitted form
            $selectedCandidates = $_POST['selected_candidates'];
        
            // Determine the maximum number of votes allowed based on the frequency
            $maxVotesAllowed = count($selectedCandidates);
        
            // Proceed to process the vote
        
            // This is optional, you can customize this part as needed
            echo "<h2>You voted for: " . implode(', ', $selectedCandidates) . "</h2>";
        
            // Prepare the SQL statement for inserting votes
            $sqlInsertVotes = $conn->prepare("INSERT INTO tie_votes (voter_id, candidate_id)
                SELECT ?, candidate_id FROM candidates
                WHERE candidate_name IN ('" . implode("', '", $selectedCandidates) . "')");
        
            // Bind the voter ID parameter
            $sqlInsertVotes->bind_param("s", $voterID);
        
            // Execute the prepared statement
            $sqlInsertVotes->execute();
        }}}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tie Panel</title>
    <!-- Uncomment the line below if you have a separate CSS file -->
    <!-- <link rel="stylesheet" type="text/css" href="style.css"> -->
</head>
<body>
    <!-- Logout button -->
    <form action="tie_loginform.php" method="post">
        <input type="submit" value="Logout">
    </form>
</body>
</html>
