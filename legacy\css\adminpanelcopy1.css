body {
    font-family: 'Urbanist', sans-serif;
    background-color: #f0f0f0;
    text-align: center;
    margin: 0;
    padding: 0;
}

.header {
    background: #fff;
    color: #333;
    padding: 0;
    text-align: center;
}

/* Logo and Navigation */
.logo-container {
    display: flex;
    align-items: center;
}

.container-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 100px;
    margin: 0;
}

.logo {
    width: 50px; 
    margin-right: 10px;
}

.logo-container b {
    font-size: 16px; 
    font-weight: bold;
    color: #243328;
}

.nav-center {
    display: flex;
    gap: 30px;
    font-size: 14px;
}

.nav-button {
    font-size: 14px;
    font-weight: bold;
    color: #243328;
    text-transform: uppercase;
    text-decoration: none;
    letter-spacing: 0.15em;
    padding: 15px 20px;
    transition: background 0.3s;
}

.nav-button:hover {
    background: #f0f0f0; 
}

/* General Links */
a {
    text-decoration: none;
    color: inherit;
}

/* Form and Form Section */
form {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    margin: 40px auto; /* Center the form horizontally */
    max-width: 600px; /* Set max width for form */
}
/* Form and Form Section */
.form-section {
    background-image: url('ff/bgg1.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 0;
    margin: 0;
    height: calc(100vh - 80px); /* Adjust the height based on the header height */
    display: flex;
    justify-content: center;
    align-items: center;
}

.form-container {
    border-radius: 10px;
    max-width: 600px; /* Set max width for form */
    width: 100%; /* Make sure it scales well on smaller screens */
    box-sizing: border-box;
    text-align: center; /* Center text horizontally */
    padding-top: 0px;
}


.highlight {
    background-color: #ffffcc; 
    font-weight: bold; 
}

/* Admin Container */
.admin-container {
    position: relative; 
    width: auto; 
    height: auto; 
    display: inline-block; 

    display: flex;
    justify-content: center;
     align-items: center; 
}

.admin-container img {
    width: 100%; 
    height: auto; 
    display: block; 
    border-radius: 10px; 
    align-items: center;
}
.top35 {
    position: absolute; 
    top: 50%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
    display: flex; 
    flex-direction: column; 
    align-items: center; 
    gap: 20px; 
}

.top35 h1 {
    color: #3D8B65;
    margin: 0;
}

.top35 a {
    font-size: 18px; 
    font-weight: bold;
    color: #FFFFFF;
    text-decoration: none;
    padding: 10px 20px; 
    background-color: #3D8A65;
    border-radius: 20px; 
    transition: background-color 0.3s; 
    padding: 15px 65px;
}

.top35 a:hover {
    background-color: #144b30;
    color: rgb(255, 255, 255); 
}

/* Add this rule to style the container for the links */
.link-container {
    display: flex;
    gap: 40px; /* Space between the links */
    justify-content: center; /* Center the links horizontally */
}
