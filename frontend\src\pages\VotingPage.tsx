import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { VoterDashboard } from '@/components/voter/VoterDashboard'
import { VotingInterface } from '@/components/voting/VotingInterface'
import { useAuth } from '@/hooks/useAuth'
import { useVotingEligibility, useVotingStatus } from '@/hooks/useVoting'
import { AlertCircle, CheckCircle, Clock, Users, Vote } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const VotingPage = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const { data: votingStatus, isLoading: statusLoading } = useVotingStatus()
  const eligibility = useVotingEligibility()
  const [showVotingInterface, setShowVotingInterface] = useState(false)

  // Redirect if not authenticated or not a voter
  useEffect(() => {
    if (!user) {
      navigate('/login')
      return
    }

    if (user.role !== 'voter' && user.role !== 'admin' && user.role !== 'execom') {
      navigate('/')
      return
    }
  }, [user, navigate])

  // Handle successful vote submission
  const handleVoteSubmitted = () => {
    navigate('/results', {
      state: {
        message: 'Your votes have been submitted successfully!',
        showConfirmation: true,
      },
    })
  }

  if (statusLoading || !votingStatus) {
    return (
      <div className='bg-background min-h-screen py-8'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>
            <Skeleton className='h-12 w-64' />
            <Skeleton className='h-32 w-full' />
            <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className='h-48' />
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show voting status card if user cannot vote
  if (!eligibility.canVote) {
    return (
      <div className='bg-background min-h-screen py-8'>
        <div className='mx-auto max-w-4xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>
            {/* Header */}
            <div className='text-center'>
              <h1 className='font-heading text-3xl font-bold'>DFPTA E-Voting</h1>
              <p className='text-muted-foreground mt-2'>Department of Education - Camarines Sur</p>
            </div>

            {/* Status Card */}
            <Card className='mx-auto max-w-2xl'>
              <CardHeader className='text-center'>
                <div className='mb-4 flex justify-center'>
                  {votingStatus.hasVoted ? (
                    <CheckCircle className='h-16 w-16 text-green-600' />
                  ) : votingStatus.votingStatus === 'no-active-session' ? (
                    <Clock className='h-16 w-16 text-orange-600' />
                  ) : (
                    <AlertCircle className='h-16 w-16 text-red-600' />
                  )}
                </div>
                <CardTitle className='text-xl'>
                  {votingStatus.hasVoted ? 'Vote Submitted' : 'Voting Not Available'}
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4 text-center'>
                <p className='text-muted-foreground'>{eligibility.reason}</p>

                {/* User info */}
                <div className='bg-muted rounded-lg p-4'>
                  <div className='mb-2 flex items-center justify-center space-x-2'>
                    <Users className='h-4 w-4' />
                    <span className='font-medium'>{user?.municipality}</span>
                  </div>
                  <Badge variant='secondary'>{user?.role}</Badge>
                </div>

                {/* Action buttons */}
                <div className='flex flex-col justify-center gap-3 sm:flex-row'>
                  <Button variant='outline' onClick={() => navigate('/results')}>
                    View Results
                  </Button>

                  <Button variant='outline' onClick={() => navigate('/')}>
                    Back to Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Show voter dashboard by default, voting interface when requested
  if (showVotingInterface && eligibility.canVote) {
    return (
      <div className='bg-background min-h-screen py-8'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>
            {/* Header */}
            <div className='flex items-center justify-between'>
              <div className='flex-1 text-center'>
                <h1 className='font-heading text-3xl font-bold'>DFPTA E-Voting</h1>
                <p className='text-muted-foreground mt-2'>
                  Department of Education - Camarines Sur
                </p>
              </div>
              <Button variant='outline' onClick={() => setShowVotingInterface(false)}>
                Back to Dashboard
              </Button>
            </div>

            {/* Voting session info */}
            <Card>
              <CardContent className='py-4'>
                <div className='flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0'>
                  <div className='flex items-center space-x-4'>
                    <div className='flex items-center space-x-2'>
                      <Vote className='text-primary h-5 w-5' />
                      <span className='font-medium'>Voting Session</span>
                    </div>
                    <Badge variant='default'>
                      {votingStatus.votingStatus === 'active'
                        ? 'Active'
                        : votingStatus.votingStatus}
                    </Badge>
                  </div>

                  <div className='text-muted-foreground flex items-center space-x-4 text-sm'>
                    <div className='flex items-center space-x-2'>
                      <Users className='h-4 w-4' />
                      <span>{user?.municipality}</span>
                    </div>
                    <Badge variant='secondary'>{user?.role}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Voting instructions */}
            <Card className='border-blue-200 bg-blue-50'>
              <CardContent className='py-4'>
                <div className='flex items-start space-x-3'>
                  <AlertCircle className='mt-0.5 h-5 w-5 flex-shrink-0 text-blue-600' />
                  <div className='text-sm text-blue-900'>
                    <p className='mb-1 font-medium'>Voting Instructions:</p>
                    <ul className='space-y-1 text-blue-800'>
                      <li>• Select up to {votingStatus.maxCandidatesPerVote} candidates</li>
                      <li>• You can search and filter candidates to help with your selection</li>
                      <li>• Review your choices carefully before submitting</li>
                      <li>• Once submitted, your votes cannot be changed</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Voting Interface */}
            <VotingInterface
              maxSelections={votingStatus.maxCandidatesPerVote}
              onVoteSubmitted={handleVoteSubmitted}
            />
          </div>
        </div>
      </div>
    )
  }

  // Show integrated voter dashboard with voting interface
  return <VoterDashboard onStartVoting={() => setShowVotingInterface(true)} />
}

export default VotingPage
