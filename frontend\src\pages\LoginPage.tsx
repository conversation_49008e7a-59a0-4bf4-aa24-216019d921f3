import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/hooks/useAuth'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, Eye, EyeOff, Loader2, Lock, User } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'
import { z } from 'zod'

// Validation schema
const loginSchema = z.object({
  username: z
    .string()
    .min(1, 'Username is required')
    .min(3, 'Username must be at least 3 characters'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
})

type LoginFormData = z.infer<typeof loginSchema>

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const navigate = useNavigate()
  const { login, user } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    clearErrors,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
  })

  // Redirect if already authenticated
  useEffect(() => {
    if (user) {
      if (user.role === 'admin') {
        navigate('/admin')
      } else if (user.role === 'execom') {
        navigate('/execom')
      } else {
        navigate('/vote')
      }
    }
  }, [user, navigate])

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    clearErrors()

    try {
      const success = await login(data)

      if (!success) {
        setError('root', {
          type: 'manual',
          message: 'Invalid username or password. Please check your credentials.',
        })
      }
      // The useEffect will handle the redirect based on user role
    } catch (error: any) {
      setError('root', {
        type: 'manual',
        message: error.message || 'Network error. Please try again.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className='flex min-h-screen items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 p-4'>
      <div className='w-full max-w-md space-y-8'>
        {/* Header */}
        <div className='text-center'>
          <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-100'>
            <Lock className='h-8 w-8 text-primary-600' />
          </div>
          <h2 className='font-heading text-3xl font-bold text-gray-900'>Welcome Back</h2>
          <p className='mt-2 text-sm text-gray-600'>
            Enter your credentials to access the DFPTA E-Voting System
          </p>
        </div>

        <Card className='shadow-lg'>
          <CardHeader className='space-y-1'>
            <CardTitle className='text-center text-2xl font-bold'>Login</CardTitle>
            <p className='text-muted-foreground text-center text-sm'>
              Sign in to your municipality account
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
              {/* Username Field */}
              <div className='space-y-2'>
                <Label htmlFor='username' className='text-sm font-medium'>
                  Municipality Name
                </Label>
                <div className='relative'>
                  <User className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='username'
                    type='text'
                    placeholder='Enter your municipality name'
                    className={`pl-10 ${errors.username ? 'border-red-500 focus:border-red-500' : ''}`}
                    {...register('username')}
                    disabled={isLoading}
                  />
                </div>
                {errors.username && (
                  <div className='flex items-center gap-1 text-sm text-red-600'>
                    <AlertCircle className='h-4 w-4' />
                    {errors.username.message}
                  </div>
                )}
              </div>

              {/* Password Field */}
              <div className='space-y-2'>
                <Label htmlFor='password' className='text-sm font-medium'>
                  Password
                </Label>
                <div className='relative'>
                  <Lock className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='password'
                    type={showPassword ? 'text' : 'password'}
                    placeholder='Enter your password'
                    className={`pl-10 pr-10 ${errors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                    {...register('password')}
                    disabled={isLoading}
                  />
                  <button
                    type='button'
                    className='absolute right-3 top-3 text-gray-400 hover:text-gray-600'
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
                  </button>
                </div>
                {errors.password && (
                  <div className='flex items-center gap-1 text-sm text-red-600'>
                    <AlertCircle className='h-4 w-4' />
                    {errors.password.message}
                  </div>
                )}
              </div>

              {/* Form Error */}
              {errors.root && (
                <div className='rounded-md border border-red-200 bg-red-50 p-3'>
                  <div className='flex items-center gap-2 text-sm text-red-800'>
                    <AlertCircle className='h-4 w-4' />
                    {errors.root.message}
                  </div>
                </div>
              )}

              <Button type='submit' className='h-11 w-full' disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>

            {/* Test Credentials */}
            <div className='mt-6 rounded-lg bg-gray-50 p-4'>
              <h4 className='mb-3 text-sm font-semibold text-gray-900'>Test Credentials</h4>
              <div className='space-y-2 text-sm'>
                <div className='flex items-center justify-between'>
                  <span className='text-gray-600'>Admin:</span>
                  <Badge variant='secondary' className='font-mono text-xs'>
                    admin / socmob123
                  </Badge>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-gray-600'>Municipality:</span>
                  <Badge variant='outline' className='font-mono text-xs'>
                    cabusao / cabu=538 (generated)
                  </Badge>
                </div>
                <p className='mt-2 text-xs text-gray-500'>
                  Municipality passwords follow format: 4 letters + symbol + 3 numbers
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default LoginPage
