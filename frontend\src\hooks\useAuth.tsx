import { createContext, ReactNode, useContext, useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { authService } from '../services/authService'
import { AuthResponse, LoginCredentials, User } from '../types'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<boolean>
  logout: (showConfirmation?: boolean) => Promise<boolean>
  refreshAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check for existing auth on mount
  useEffect(() => {
    const initAuth = () => {
      try {
        const token = localStorage.getItem('token')
        const userStr = localStorage.getItem('user')

        if (token && userStr) {
          const userData = JSON.parse(userStr)
          setUser(userData)
        }
      } catch (error) {
        // Invalid data in localStorage
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('user')
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [])

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      setIsLoading(true)
      const response: AuthResponse = await authService.login(credentials)

      if (response.success) {
        setUser(response.user)
        localStorage.setItem('token', response.token)
        localStorage.setItem('user', JSON.stringify(response.user))

        // Only store refresh token if it exists
        if (response.refreshToken) {
          localStorage.setItem('refreshToken', response.refreshToken)
        }

        toast.success(`Welcome back, ${response.user.municipality}!`)
        return true
      } else {
        // Don't show toast error here - let the LoginPage handle it
        return false
      }
    } catch (error: any) {
      // Don't show toast error here - let the LoginPage handle it
      throw error // Re-throw so LoginPage can catch and display
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (showConfirmation: boolean = false) => {
    if (showConfirmation) {
      const confirmed = window.confirm('Are you sure you want to log out?')
      if (!confirmed) {
        return false
      }
    }

    try {
      // Call backend logout to invalidate refresh tokens
      await authService.logout()
    } catch (error) {
      // Continue with logout even if backend call fails
      console.warn('Backend logout failed:', error)
    }

    // Clear local storage and state
    setUser(null)
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')

    toast.success('Logged out successfully')

    // Redirect to login page
    window.location.href = '/login'

    return true
  }

  const refreshAuth = async () => {
    try {
      const userData = await authService.getCurrentUser()
      setUser(userData)
    } catch (error) {
      logout()
    }
  }

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    logout,
    refreshAuth,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
