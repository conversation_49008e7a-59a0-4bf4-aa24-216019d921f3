body {
    font-family: 'Urbanist', sans-serif;
    background-color: #f0f0f0;
    text-align: center;
    margin: 0;
    padding: 0;
}

/* Center and responsive container */
.container-nav, .container-footer, .container {
    width: 100%; 
    max-width: 1200px; 
    margin: 0 auto;
    padding: 0 10px; 
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header {
    background: #D3DCE5;
    padding: 25px 0; /*25*/
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 70px;
    display: block;
    height: auto;
    margin-right: 10px;
}

/* Center logo text */
.logo-text b {
    font-family: 'Urbanist', sans-serif;
    font-size: 18px;
    font-weight: bold;
    color: #243328;
}

/* Navigation styling */
.nav-center {
    display: flex;
    gap: 20px;
}

.nav-button {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    padding: 10px 15px;
    position: relative;
    text-transform: capitalize;
    letter-spacing: 0.15em;
}

.nav-button:hover {
    background: #d3dce5;
}

/* Content styling */
.content {
    background-image: url('ff/admin.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
    color: #2D4863;
  /*  padding: 10px 0; */
    padding: 32px 42px; 
    width: 100%;
    display: flex; /* flex*/
    justify-content: center; 
    align-items: center; 
    flex: 1; 
    text-align: center; 
    flex-direction: column; 
}

/* General Links */
a {
    text-decoration: none;
    color: inherit;
}

form {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    margin: 40px auto; 
    max-width: 600px;
}
/* Form and Form Section */
.form-section {
    background-image: url('ff/admin.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 0;
    margin: 0;
    height: calc(100vh - 80px); 
    display: flex;
    justify-content: center;
    align-items: center;
}

.form-container {
    border-radius: 10px;
    max-width: 600px;
    width: 100%; 
    box-sizing: border-box;
    text-align: center;
    padding-top: 0px;
}


.highlight {
    background-color: #ffffcc; 
    font-weight: bold; 
}

/* Admin Container */
.admin-container {
    position: relative; 
    width: auto; 
    height: auto; 
    display: inline-block; 
    display: flex;
    justify-content: center;
     align-items: center; 
}

.admin-container img {
    width: 100%; 
    height: 100%; /*auto*/ 
    display: block; 
    border-radius: 10px; 
    align-items: center;
    margin-top: -100px;

}
.top35 {
    position: absolute; 
    top: 50%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
    display: flex; 
    flex-direction: column; 
    align-items: center; 
    gap: 20px; 
}

.top35 h1 {
    color: #3D8B65;
    margin-top: -120px;
    font-weight: bolder;
}

.top35 a {
    font-size: 16px; 
    font-weight: bold;
    color: #FFFFFF;
    text-decoration: none;
    padding: 10px 20px; 
    background-color: #3D8A65;
    border-radius: 20px; 
    transition: background-color 0.3s; 
    padding: 15px 65px;
}

.top35 a:hover {
    background-color: #144b30;
    color: rgb(255, 255, 255); 
}


.link-container {
    display: flex;
    gap: 40px; 
    justify-content: left;
}

/* Footer styling */
.footer {
    background: #fff;
    padding: 5px 0;
    text-align: center; 
}

.container-footer {
    display: flex; 
    justify-content: center; 
    align-items: center; 
    width: 100%; 
}

.footer-text {
    display: flex; 
    justify-content: center;
    align-items: right; 
    font-size: 12px;
    color: #2d4863;
    margin: 0; 
}