<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start(); // Start a session

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Retrieve username and password from the login form
    $username = $_POST["username"];
    $password = $_POST["password"];

    $userMappings = [
        "Baao" => 1,
        "Balatan" => 2,
        "Bato" => 3,
        "Bombon" => 4,
        "Buhi" => 5,
        "Bula" => 6,
        "Cabusao" => 7,
        "Calabanga" => 8,
        "Camaligan" => 9,
        "Canaman" => 10,
        "Caramoan" => 11,
        "Del Gallego" => 12,
        "Gainza" => 13,
        "Garchitorena" => 14,
        "Goa" => 15,
        "Lagonoy" => 16,
        "Libmanan" => 17,
        "Lupi" => 18,
        "Magarao" => 19,
        "<PERSON>laor" => 20,
        "Minalabac" => 21,
        "Nabua" => 22,
        "Ocampo" => 23,
        "<PERSON><PERSON><PERSON><PERSON>" => 24,
        "Pasacao" => 25,
        "<PERSON><PERSON><PERSON><PERSON>" => 26,
        "<PERSON><PERSON>" => 27,
        "Raga<PERSON>" => 28,
        "<PERSON>gna<PERSON>" => 29,
        "San Fernando" => 30,
        "San Jose" => 31,
        "Sipocot" => 32,
        "Siruma" => 33,
        "Tigaon" => 34,
        "Tinambac" => 35
    ];
    // Define an array of pre-registered accounts
    $preRegisteredAccounts = [
        "Baao" => "baao",
        "Balatan" => "balatan",
        "Bato" => "bato",
        "Bombon" => "bombon",
        "Buhi" => "buhi",
        "Bula" => "bula",
        "Cabusao" => "cabusao",
        "Calabanga" => "calabanga",
        "Camaligan" => "camaligan",
        "Canaman" => "canaman",
        "Caramoan" => "caramoan",
        "Del Gallego" => "delgallego",
        "Gainza" => "gainza",
        "Garchitorena" => "garchitorena",
        "Goa" => "goa",
        "Lagonoy" => "lagonoy",
        "Libmanan" => "libmanan",
        "Lupi" => "lupi",
        "Magarao" => "magarao",
        "Milaor" => "milaor",
        "Minalabac" => "minalabac",
        "Nabua" => "nabua",
        "Ocampo" => "ocampo",
        "Pamplona" => "pamplona",
        "Pasacao" => "pasacao",
        "Parubcan" => "parubcan",
        "Pili" => "pili",
        "Ragay" => "ragay",
        "Sagnay" => "sagnay",
        "San Fernando" => "sanfernando",
        "San Jose" => "sanjose",
        "Sipocot" => "sipocot",
        "Siruma" => "siruma",
        "Tigaon" => "tigaon",
        "Tinambac" => "tinambac"
    ];
  // Check if the entered credentials match a pre-registered account
  if (array_key_exists($username, $preRegisteredAccounts) && $preRegisteredAccounts[$username] === $password) {
        // Authentication successful
        $_SESSION['user_id'] = $userMappings[$username]; // Use the mapped integer ID as the user identifier
        $_SESSION['voted'] = false; // Initialize the voted status as false
        header("Location: vote.html"); // Redirect to the voting page
        exit();
} else {
        // Invalid credentials, set an error message in the session
        $_SESSION['error_message'] = 'Incorrect name or password';
        header("Location: login.php"); // Redirect to the login page
        exit();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>DFPTA E-Voting System</title>
    <link rel="stylesheet" type="text/css" href="login.css"> <!-- Link to your CSS file -->
    <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@400;700&display=swap" rel="stylesheet">
    <link rel="icon" href="dfptalogo.png" type="image/x-icon"> 
</head>


<header class="header">
    <div class="container-nav">
        <div class="logo-container">
            <img src="dfptalogo.png" alt="Online Voting System Logo" class="logo">
            <b> DFPTA - SDO CAMARINES SUR </b>
        </div>
        <div class="nav-center">
            <a class="nav-button" href = "index.html"> HOME</a>
            <a class="nav-button" href="admin_login.html">ADMIN LOGIN</a>
            <a class="nav-button" href="contact.html">CONTACT ADMIN</a>
        </div>
    </div>
</header>

<body>
   <section class="login-section" style="background-image: url('ff/bgg1.png');">
    <div class="login-container"> 
        <h2 class="section-title">USER LOGIN</h2>

        <form action="login.php" method="post">
            <label for="username">Municipality</label>
            <input type="text" name="username" placeholder="Municipality" required><br>
            
            <label for="password">Password</label>
            <input type="password" name="password" placeholder="Password" required><br>

            <!-- Display the error message if it exists -->
            <?php if (isset($_SESSION['error_message'])) : ?>
                <p class="ermessage"><?php echo $_SESSION['error_message']; ?></p>
            <?php endif; ?>

            <input type="submit" value="Login">
        </form>
    </div> 
</section>

<footer class="footer">
    <div class="container-footer">
        <div class="footer-text">
            &copy; 2024 Social Mobilization and Networking - SDO Camarines Sur. All rights reserved.
        </div>
    </div>
</footer>
</body>
</html>