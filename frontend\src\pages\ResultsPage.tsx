import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { usePublicResults } from '@/hooks/useVoting'
import { AlertCircle, Users } from 'lucide-react'

const ResultsPage = () => {
  const { data: resultsData, isLoading, error } = usePublicResults()

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='mx-auto max-w-6xl px-4 sm:px-6 lg:px-8'>
        {/* Partial & Unofficial Results Header */}
        <div className='mb-8 text-center'>
          <h1 className='mb-4 text-4xl font-bold text-red-600'>PARTIAL & UNOFFICIAL RESULTS</h1>
          <div className='mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4'>
            <div className='flex items-center justify-center gap-2 text-yellow-800'>
              <AlertCircle className='h-5 w-5' />
              <p className='font-medium'>These results are preliminary and subject to change</p>
            </div>
          </div>
          <h2 className='font-heading text-2xl font-bold text-gray-900'>DFPTA Election Results</h2>
          <p className='mt-2 text-gray-600'>Department of Education - Camarines Sur</p>
        </div>

        {/* Turnout Statistics */}
        {resultsData?.totalStats && (
          <Card className='mb-8'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='h-5 w-5' />
                Voter Turnout
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
                <div className='text-center'>
                  <div className='text-3xl font-bold text-blue-600'>
                    {resultsData.totalStats.participationRate.toFixed(1)}%
                  </div>
                  <div className='text-sm text-gray-600'>Turnout Rate</div>
                </div>
                <div className='text-center'>
                  <div className='text-3xl font-bold text-green-600'>
                    {resultsData.totalStats.totalVotesCast}
                  </div>
                  <div className='text-sm text-gray-600'>Votes Cast</div>
                </div>
                <div className='text-center'>
                  <div className='text-3xl font-bold text-gray-600'>
                    {resultsData.totalStats.totalVoters}
                  </div>
                  <div className='text-sm text-gray-600'>Eligible Voters</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Results Content */}
        <Card>
          <CardContent className='p-8'>
            {isLoading ? (
              <div className='text-center'>
                <div className='mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600'></div>
                <p className='text-gray-500'>Loading results...</p>
              </div>
            ) : error ? (
              <div className='text-center text-red-600'>
                <AlertCircle className='mx-auto mb-4 h-12 w-12' />
                <p>Error loading results. Please try again later.</p>
              </div>
            ) : (
              <div className='text-center text-gray-500'>
                <p className='text-lg'>
                  Detailed results will be available once voting is complete
                </p>
                <p className='mt-2 text-sm'>Check back later for the final official results</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ResultsPage
