import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { useDistrictsAndMunicipalities } from '@/hooks/useSystem'
import { useCandidateSelection, useCandidates, useSubmitVotes } from '@/hooks/useVoting'
import { Candidate } from '@/services/votingService'
import { CheckSquare, Grid, List, RotateCcw, Search, Square } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { CandidateCard, CandidateCardCompact } from './CandidateCard'

interface VotingInterfaceProps {
  maxSelections: number
  onVoteSubmitted?: () => void
}

export function VotingInterface({ maxSelections, onVoteSubmitted }: VotingInterfaceProps) {
  // State
  const [searchTerm, setSearchTerm] = useState('')
  const [municipalityFilter, setMunicipalityFilter] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showDetails, setShowDetails] = useState(false)

  // Hooks
  const { data: candidatesData, isLoading: candidatesLoading } = useCandidates()
  const { data: systemData, isLoading: systemLoading } = useDistrictsAndMunicipalities()
  const {
    selectedCandidates,
    selectedCount,
    remainingSelections,
    canSelectMore,
    validation,
    toggleCandidate,
    selectAll,
    clearAll,
    isSelected,
  } = useCandidateSelection(maxSelections)

  const submitVotesMutation = useSubmitVotes()

  // Filter candidates
  const filteredCandidates =
    candidatesData?.candidates?.filter((candidate: Candidate) => {
      if (!candidate) return false

      const matchesSearch =
        candidate.municipalityName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        false ||
        candidate.district?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        false
      const matchesMunicipality =
        municipalityFilter === 'all' ||
        !municipalityFilter ||
        candidate.district === municipalityFilter
      return matchesSearch && matchesMunicipality
    }) || []

  // Handle vote submission with enhanced feedback
  const handleSubmitVotes = () => {
    if (!validation.isValid) return

    submitVotesMutation.mutate(selectedCandidates, {
      onSuccess: data => {
        // Show success message with details
        toast.success(
          `Successfully submitted ${data.votesCount} votes! Thank you for participating.`
        )
        onVoteSubmitted?.()
      },
      onError: (error: any) => {
        // Enhanced error handling
        const message = error.response?.data?.error || 'Failed to submit votes. Please try again.'
        toast.error(message)
      },
    })
  }

  // Handle select all filtered candidates
  const handleSelectAllFiltered = () => {
    const candidateIds = filteredCandidates.map(c => c._id)
    selectAll(candidateIds)
  }

  if (candidatesLoading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-10 w-32' />
        </div>
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className='h-48' />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-3'>
      {/* Compact Header with Progress */}
      <Card className='border-l-primary border-l-2'>
        <CardHeader className='pb-3'>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>Select Your Candidates</CardTitle>
            <div className='flex items-center space-x-3'>
              <Badge
                variant={selectedCount === maxSelections ? 'default' : 'secondary'}
                className='px-2 py-1 text-sm'
              >
                {selectedCount} / {maxSelections} Selected
              </Badge>
              {remainingSelections > 0 && (
                <span className='text-muted-foreground text-xs'>
                  {remainingSelections} remaining
                </span>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          <div className='mt-4'>
            <div className='mb-2 flex items-center justify-between text-sm text-gray-600'>
              <span>Selection Progress</span>
              <span>{Math.round((selectedCount / maxSelections) * 100)}% Complete</span>
            </div>
            <div className='h-2 w-full rounded-full bg-gray-200'>
              <div
                className='bg-primary h-2 rounded-full transition-all duration-300 ease-in-out'
                style={{ width: `${(selectedCount / maxSelections) * 100}%` }}
              />
            </div>
            {selectedCount === maxSelections && (
              <p className='mt-2 flex items-center gap-1 text-sm text-green-600'>
                <CheckSquare className='h-4 w-4' />
                Ready to submit your votes!
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and filters */}
          <div className='flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0'>
            <div className='flex-1'>
              <div className='relative'>
                <Search className='text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2' />
                <Input
                  placeholder='Search candidates by name or municipality...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>

            <Select value={municipalityFilter} onValueChange={setMunicipalityFilter}>
              <SelectTrigger className='w-full md:w-48'>
                <SelectValue placeholder='Filter by municipality' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All municipalities</SelectItem>
                {systemData?.municipalities?.map((municipality: string) => (
                  <SelectItem key={municipality} value={municipality}>
                    {municipality}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Action buttons */}
          <div className='mt-4 flex flex-wrap items-center justify-between gap-4'>
            <div className='flex items-center space-x-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={handleSelectAllFiltered}
                disabled={!canSelectMore || filteredCandidates.length === 0}
              >
                <CheckSquare className='mr-2 h-4 w-4' />
                Select All Filtered ({filteredCandidates.length})
              </Button>

              <Button variant='outline' size='sm' onClick={clearAll} disabled={selectedCount === 0}>
                <Square className='mr-2 h-4 w-4' />
                Clear All
              </Button>
            </div>

            <div className='flex items-center space-x-2'>
              <Button variant='outline' size='sm' onClick={() => setShowDetails(!showDetails)}>
                {showDetails ? 'Hide' : 'Show'} Details
              </Button>

              <div className='flex items-center rounded-md border'>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => setViewMode('grid')}
                  className='rounded-r-none'
                >
                  <Grid className='h-4 w-4' />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => setViewMode('list')}
                  className='rounded-l-none'
                >
                  <List className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Candidates grid/list */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <h3 className='text-lg font-semibold'>Candidates ({filteredCandidates.length})</h3>
          {searchTerm || municipalityFilter ? (
            <Button
              variant='ghost'
              size='sm'
              onClick={() => {
                setSearchTerm('')
                setMunicipalityFilter('')
              }}
            >
              <RotateCcw className='mr-2 h-4 w-4' />
              Clear Filters
            </Button>
          ) : null}
        </div>

        {filteredCandidates.length === 0 ? (
          <Card>
            <CardContent className='py-8 text-center'>
              <p className='text-muted-foreground'>No candidates found matching your criteria.</p>
            </CardContent>
          </Card>
        ) : (
          <div
            className={
              viewMode === 'grid' ? 'grid gap-4 md:grid-cols-2 lg:grid-cols-3' : 'space-y-3'
            }
          >
            {filteredCandidates.map(candidate =>
              viewMode === 'grid' ? (
                <CandidateCard
                  key={candidate._id}
                  candidate={candidate}
                  isSelected={isSelected(candidate._id)}
                  onToggle={toggleCandidate}
                  disabled={!canSelectMore && !isSelected(candidate._id)}
                  showDetails={showDetails}
                />
              ) : (
                <CandidateCardCompact
                  key={candidate._id}
                  candidate={candidate}
                  isSelected={isSelected(candidate._id)}
                  onToggle={toggleCandidate}
                  disabled={!canSelectMore && !isSelected(candidate._id)}
                />
              )
            )}
          </div>
        )}
      </div>

      {/* Submit button */}
      {selectedCount > 0 && (
        <Card className='sticky bottom-4 shadow-lg'>
          <CardContent className='py-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='font-medium'>
                  {selectedCount} candidate{selectedCount !== 1 ? 's' : ''} selected
                </p>
                {!validation.isValid && (
                  <p className='text-destructive mt-1 text-sm'>{validation.errors[0]}</p>
                )}
              </div>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size='lg'
                    disabled={!validation.isValid || submitVotesMutation.isLoading}
                    className='min-w-32'
                  >
                    {submitVotesMutation.isLoading ? 'Submitting...' : 'Submit Votes'}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className='max-w-md'>
                  <AlertDialogHeader>
                    <AlertDialogTitle className='flex items-center gap-2'>
                      <CheckSquare className='text-primary h-5 w-5' />
                      Confirm Your Votes
                    </AlertDialogTitle>
                    <AlertDialogDescription className='space-y-3'>
                      <p>
                        You are about to submit votes for {selectedCount} candidate
                        {selectedCount !== 1 ? 's' : ''}:
                      </p>
                      <div className='max-h-32 overflow-y-auto rounded-lg bg-gray-50 p-3'>
                        <ul className='space-y-1 text-sm'>
                          {selectedCandidates.map(candidateId => {
                            const candidate = candidatesData?.candidates?.find(
                              c => c._id === candidateId
                            )
                            return candidate ? (
                              <li key={candidateId} className='flex items-center gap-2'>
                                <CheckSquare className='h-3 w-3 text-green-600' />
                                <span className='font-medium'>{candidate.municipalityName}</span>
                                <span className='text-gray-500'>({candidate.district})</span>
                              </li>
                            ) : null
                          })}
                        </ul>
                      </div>
                      <p className='text-sm font-medium text-amber-600'>
                        ⚠️ This action cannot be undone. Please review your selections carefully.
                      </p>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Review Again</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleSubmitVotes}
                      className='bg-primary hover:bg-primary/90'
                    >
                      {submitVotesMutation.isLoading ? (
                        <>
                          <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                          Submitting...
                        </>
                      ) : (
                        'Confirm & Submit Votes'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
