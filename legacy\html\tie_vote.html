<!DOCTYPE html>
<html>
<head>
    <title>Vote</title>
    <link rel="stylesheet" type="text/css" href="vote.css">
    <script>
        function validateForm() {
            var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            if (checkboxes.length > 15) {
                alert('You can vote for up to 15 candidates only.');
                return false;
            }
            return true;
        }

        function limitCheckboxes() {
            var checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].addEventListener('change', function() {
                    var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                    if (checkedCheckboxes.length > 15) {
                        alert('You can vote for up to 15 candidates only.');
                        this.checked = false;
                    }
                });
            }
        }

        window.onload = limitCheckboxes;
    </script>
</head>
<body>
    <h1>Cast Your Vote</h1>
    <form action="vote.php" method="post" onsubmit="return validateForm();">
        <label>Choose Your Candidate:</label><br>
        <div class="candidates">
            <?php if (isset($_SESSION['isTie']) && $_SESSION['isTie']) : ?>
                <?php foreach ($_SESSION['tiedCandidates'] as $candidate) : ?>
                    <div>
                        <input type="checkbox" name="candidate_id[]" value="<?= $candidate['candidate_id'] ?>">
                        <?= $candidate['candidate_name'] ?>
                    </div>
                <?php endforeach; ?>
            <?php else : ?>
                <p>No tie. Please proceed to vote for your preferred candidates.</p>
            <?php endif; ?>
        </div>
        <input type="submit" value="<?php echo isset($_SESSION['isTie']) && $_SESSION['isTie'] ? 'Vote' : 'Logout'; ?>">
    </form>
</body>
</html>
