import mongoose from 'mongoose'

const voteSchema = new mongoose.Schema(
  {
    voterId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Voter ID is required'],
      index: true,
    },
    candidateId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Candidate',
      required: [true, 'Candidate ID is required'],
      index: true,
    },
    voteType: {
      type: String,
      enum: {
        values: ['regular'],
        message: 'Invalid vote type',
      },
      required: [true, 'Vote type is required'],
      default: 'regular',
    },
    sessionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'VotingSession',
      required: [true, 'Session ID is required'],
      index: true,
    },
    // Additional metadata
    voterMunicipality: {
      type: String,
      required: [true, 'Voter municipality is required'],
      enum: [
        'Baao',
        'Balatan',
        '<PERSON><PERSON>',
        '<PERSON>on',
        '<PERSON>uh<PERSON>',
        '<PERSON><PERSON>',
        'Cabusao',
        'Calabanga',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON> Gallego',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'Goa',
        '<PERSON>gonoy',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        'Milaor',
        'Minalabac',
        'Nabua',
        'Ocampo',
        'Pamplona',
        'Pasacao',
        'Parubcan',
        'Pili',
        'Ragay',
        'Sagnay',
        'San Fernando',
        'San Jose',
        'Sipocot',
        'Siruma',
        'Tigaon',
        'Tinambac',
      ],
    },
    candidateMunicipality: {
      type: String,
      required: [true, 'Candidate municipality is required'],
    },
    // Audit trail
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true,
    },
    // Vote verification
    voteHash: {
      type: String,
      unique: true,
      sparse: true,
    },
    isVerified: {
      type: Boolean,
      default: true,
    },
    // Batch voting (for multiple candidates in one submission)
    batchId: {
      type: String,
      index: true,
    },
    batchPosition: {
      type: Number,
      min: 1,
    },
    totalBatchSize: {
      type: Number,
      min: 1,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        delete ret.ipAddress
        delete ret.userAgent
        delete ret.voteHash
        return ret
      },
    },
  }
)

// Compound indexes for performance and uniqueness (simplified for single-round voting)
voteSchema.index({ voterId: 1, candidateId: 1, voteType: 1 })
voteSchema.index({ sessionId: 1, voteType: 1 })
voteSchema.index({ voterMunicipality: 1, timestamp: 1 })
voteSchema.index({ candidateId: 1, voteType: 1 })
voteSchema.index({ batchId: 1, batchPosition: 1 })

// Unique constraint for regular votes (one vote per candidate per voter)
voteSchema.index(
  { voterId: 1, candidateId: 1 },
  {
    unique: true,
    partialFilterExpression: { voteType: 'regular' },
  }
)

// Virtual for anonymized voter info
voteSchema.virtual('anonymizedVoter').get(function () {
  return {
    municipality: this.voterMunicipality,
    timestamp: this.timestamp,
  }
})

// Instance method to verify vote integrity
voteSchema.methods.verifyVote = function () {
  // Implement vote verification logic
  return this.isVerified
}

// Static method to get vote counts by candidate
voteSchema.statics.getVoteCountsByCandidate = function (voteType = 'regular') {
  return this.aggregate([
    {
      $match: {
        voteType: voteType,
        isVerified: true,
      },
    },
    {
      $group: {
        _id: '$candidateId',
        voteCount: { $sum: 1 },
        voters: { $push: '$voterMunicipality' },
      },
    },
    {
      $lookup: {
        from: 'candidates',
        localField: '_id',
        foreignField: '_id',
        as: 'candidate',
      },
    },
    {
      $unwind: '$candidate',
    },
    {
      $sort: { voteCount: -1, 'candidate.createdAt': 1 },
    },
  ])
}

// Static method to get voting statistics
voteSchema.statics.getVotingStatistics = function (round = 1, voteType = 'regular') {
  return this.aggregate([
    {
      $match: {
        round: round,
        voteType: voteType,
        isVerified: true,
      },
    },
    {
      $group: {
        _id: null,
        totalVotes: { $sum: 1 },
        uniqueVoters: { $addToSet: '$voterId' },
        votingMunicipalities: { $addToSet: '$voterMunicipality' },
        firstVote: { $min: '$timestamp' },
        lastVote: { $max: '$timestamp' },
      },
    },
    {
      $project: {
        totalVotes: 1,
        uniqueVoterCount: { $size: '$uniqueVoters' },
        participatingMunicipalities: { $size: '$votingMunicipalities' },
        firstVote: 1,
        lastVote: 1,
        votingDuration: {
          $subtract: ['$lastVote', '$firstVote'],
        },
      },
    },
  ])
}

// Static method to get votes by municipality
voteSchema.statics.getVotesByMunicipality = function (round = 1, voteType = 'regular') {
  return this.aggregate([
    {
      $match: {
        round: round,
        voteType: voteType,
        isVerified: true,
      },
    },
    {
      $group: {
        _id: '$voterMunicipality',
        voteCount: { $sum: 1 },
        candidates: { $addToSet: '$candidateId' },
        timestamps: { $push: '$timestamp' },
      },
    },
    {
      $project: {
        municipality: '$_id',
        voteCount: 1,
        candidateCount: { $size: '$candidates' },
        firstVote: { $min: '$timestamps' },
        lastVote: { $max: '$timestamps' },
      },
    },
    {
      $sort: { voteCount: -1 },
    },
  ])
}

// Static method to detect potential ties
voteSchema.statics.detectTies = function (round = 1, position = 15) {
  return this.aggregate([
    {
      $match: {
        round: round,
        voteType: 'regular',
        isVerified: true,
      },
    },
    {
      $group: {
        _id: '$candidateId',
        voteCount: { $sum: 1 },
      },
    },
    {
      $sort: { voteCount: -1 },
    },
    {
      $group: {
        _id: null,
        candidates: { $push: { candidateId: '$_id', votes: '$voteCount' } },
      },
    },
    {
      $project: {
        positionVotes: { $arrayElemAt: ['$candidates.votes', position - 1] },
        tiedCandidates: {
          $filter: {
            input: '$candidates',
            cond: { $eq: ['$$this.votes', { $arrayElemAt: ['$candidates.votes', position - 1] }] },
          },
        },
      },
    },
  ])
}

// Static method to get voter participation
voteSchema.statics.getVoterParticipation = function (round = 1) {
  return this.aggregate([
    {
      $match: {
        round: round,
        isVerified: true,
      },
    },
    {
      $group: {
        _id: '$voterId',
        voteCount: { $sum: 1 },
        voteTypes: { $addToSet: '$voteType' },
        municipality: { $first: '$voterMunicipality' },
        firstVote: { $min: '$timestamp' },
        lastVote: { $max: '$timestamp' },
      },
    },
    {
      $group: {
        _id: '$municipality',
        voterCount: { $sum: 1 },
        totalVotes: { $sum: '$voteCount' },
        avgVotesPerVoter: { $avg: '$voteCount' },
      },
    },
    {
      $sort: { voterCount: -1 },
    },
  ])
}

// Pre-save middleware for audit trail
voteSchema.pre('save', function (next) {
  if (this.isNew) {
    // Generate vote hash for verification
    const crypto = require('crypto')
    const voteData = `${this.voterId}-${this.candidateId}-${this.round}-${this.voteType}-${this.timestamp}`
    this.voteHash = crypto.createHash('sha256').update(voteData).digest('hex')
  }
  next()
})

const Vote = mongoose.model('Vote', voteSchema)

export default Vote
