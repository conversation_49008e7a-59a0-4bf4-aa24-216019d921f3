<?php
// Replace with your database credentials
include 'db_connection.php';
// Connect to the database

// Start the session if not already started
session_start();

// Get the voter_id from the session
$voter_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Check if voter_id is missing or empty
if ($voter_id === null || empty($voter_id)) {
    die("Error: Voter ID is missing or empty in the session.");
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_candidate = $_POST['selected_candidate'];

    // Insert the new vote into the tie_votes table
    $insert_sql = "INSERT INTO tie_votes (voter_id, candidate_id) VALUES (?, ?)";
    $insert_stmt = $conn->prepare($insert_sql);
    $insert_stmt->bind_param("si", $voter_id, $selected_candidate);
    $insert_stmt->execute();
    $insert_stmt->close();

    echo "Vote successfully recorded. You voted for candidate ID: $selected_candidate.<br><br>";
    echo '<a href="logout.php">Logout</a>'; // Add a logout link
}

// Close the database connection
$conn->close();
?>
