import dfptaLogo from '@/assets/images/dfptalogo.png'
import { Link } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'

const Header = () => {
  const { user, logout } = useAuth()

  return (
    <header className='border-b border-gray-200 bg-white shadow-sm'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='flex h-16 items-center justify-between'>
          {/* Logo */}
          <div className='flex items-center'>
            <Link to='/' className='flex items-center space-x-3'>
              <img src={dfptaLogo} alt='DFPTA Logo' className='h-10 w-10 object-contain' />
              <div className='flex flex-col'>
                <span className='font-heading text-lg font-semibold text-gray-900'>
                  DFPTA E-Voting
                </span>
                <span className='text-xs text-gray-500'>SDO Camarines Sur</span>
              </div>
            </Link>
          </div>

          {/* Navigation */}
          <nav className='hidden items-center space-x-8 md:flex'>
            <Link
              to='/results'
              className='px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900'
            >
              Results
            </Link>
            <div className='flex items-center space-x-2'>
              {/* Theme Toggle */}
              <div className='theme-toggle-placeholder'>{/* ThemeToggle will be added here */}</div>

              {user ? (
                <div className='flex items-center space-x-4'>
                  <span className='text-sm text-gray-700'>Welcome, {user.municipality}</span>
                  <button onClick={logout} className='btn-outline text-sm'>
                    Logout
                  </button>
                </div>
              ) : (
                <Link to='/login' className='btn-primary text-sm'>
                  Login
                </Link>
              )}
            </div>
          </nav>
        </div>
      </div>
    </header>
  )
}

export default Header
