<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: execom_login.html");
    exit();
}

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Get the entered code
    $enteredCode = $_POST["entered_code"];

    // Define codes
    $chairpersonCode = "1person";
    $viceChairpersonCode = "vceh";
    $secretaryCode = "set4";
    $treasurerCode = "3sur";

    // Check the entered code and redirect accordingly
    switch ($enteredCode) {
        case $chairpersonCode:
            header("Location: chairperson.php");
            exit();
        case $viceChairpersonCode:
            header("Location: vicechairperson.php");
            exit();
        case $secretaryCode:
            header("Location: secretary.php");
            exit();
        case $treasurerCode:
            header("Location: treasurer.php");
            exit();
        default:
            // Invalid code
            $errorMessage = "Invalid code entered.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enter Code - Online Voting System</title>
</head>
<body>
    <h2>Enter the Code:</h2>
    <form action="c1.php" method="post">
        <label for="entered_code">Code:</label>
        <input type="text" name="entered_code" required><br>
        <input type="submit" value="Submit">
    </form>
</body>
</html>
