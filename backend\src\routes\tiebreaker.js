import express from 'express'
import {
  getActiveTieBreaker,
  submitTieBreakerVote,
  getTieBreakerResults,
  detectAndCreateTieBreaker,
  startTieBreaker,
  completeTieBreaker,
} from '../controllers/tieBreakerController.js'
import { protect, adminOnly } from '../middleware/auth.js'
import { validateTieBreakerVote } from '../middleware/validation.js'
import rateLimit from 'express-rate-limit'

const router = express.Router()

// Rate limiting for tie-breaker endpoints
const tieBreakerRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // limit each IP to 20 requests per windowMs
  message: {
    success: false,
    error: 'Too many tie-breaker requests, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

const tieBreakerVoteRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 3, // limit each IP to 3 vote submissions per minute
  message: {
    success: false,
    error: 'Too many tie-breaker vote attempts, please wait before trying again',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

// Protected routes - require authentication
router.use(protect)

// Get active tie-breaker
router.get('/', tieBreakerRateLimit, getActiveTieBreaker)

// Submit tie-breaker vote
router.post('/vote', tieBreakerVoteRateLimit, validateTieBreakerVote, submitTieBreakerVote)

// Get tie-breaker results
router.get('/results/:id', getTieBreakerResults)

// Admin only routes
router.use('/admin', adminOnly)

// Detect and create tie-breaker
router.post('/admin/detect', detectAndCreateTieBreaker)

// Start tie-breaker
router.post('/admin/:id/start', startTieBreaker)

// Complete tie-breaker
router.post('/admin/:id/complete', completeTieBreaker)

export default router
