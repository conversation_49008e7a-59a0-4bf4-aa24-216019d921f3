import { Toaster } from 'react-hot-toast'
import { Route, Routes } from 'react-router-dom'
import { ThemeProvider } from './components/theme-provider'
import { AuthProvider } from './hooks/useAuth'

// Pages
import AdminDashboard from './pages/AdminDashboard'
import ExecomDashboard from './pages/ExecomDashboard'
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import NotFoundPage from './pages/NotFoundPage'
import ResultsPage from './pages/ResultsPage'
import VotingPage from './pages/VotingPage'

// Components
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'

function App() {
  return (
    <ThemeProvider defaultTheme='light' storageKey='dfpta-ui-theme'>
      <AuthProvider>
        <Layout>
          <Routes>
            {/* Public routes */}
            <Route path='/' element={<HomePage />} />
            <Route path='/login' element={<LoginPage />} />
            <Route path='/results' element={<ResultsPage />} />

            {/* Protected routes */}
            <Route
              path='/vote'
              element={
                <ProtectedRoute allowedRoles={['voter']}>
                  <VotingPage />
                </ProtectedRoute>
              }
            />

            <Route
              path='/admin/*'
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path='/execom/*'
              element={
                <ProtectedRoute allowedRoles={['execom']}>
                  <ExecomDashboard />
                </ProtectedRoute>
              }
            />

            {/* 404 page */}
            <Route path='*' element={<NotFoundPage />} />
          </Routes>
        </Layout>
        <Toaster
          position='top-right'
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
