import dotenv from 'dotenv'
import mongoose from 'mongoose'
import User from '../models/User.js'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config()

/**
 * Script to clean up deprecated user roles (execom, tie-breaker)
 * and ensure system compliance with new account limits
 */
async function cleanupDeprecatedRoles() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Connected to MongoDB for role cleanup')

    // Find users with deprecated roles
    const deprecatedUsers = await User.find({
      role: { $in: ['execom', 'tie-breaker'] },
    })

    if (deprecatedUsers.length === 0) {
      logger.info('No users with deprecated roles found')
      return
    }

    logger.info(`Found ${deprecatedUsers.length} users with deprecated roles:`)
    deprecatedUsers.forEach(user => {
      logger.info(`- ${user.username} (${user.municipality}) - Role: ${user.role}`)
    })

    // Option 1: Convert deprecated roles to 'voter' (recommended)
    const convertToVoter = true // Set to false to deactivate instead

    if (convertToVoter) {
      // Check if converting all to voters would exceed limits
      const currentVoters = await User.countDocuments({ role: 'voter', isActive: true })
      const totalAfterConversion = currentVoters + deprecatedUsers.length

      if (totalAfterConversion > 35) {
        logger.warn(
          `Converting all deprecated roles would result in ${totalAfterConversion} voters (max: 35)`
        )
        logger.info('Deactivating excess users instead of converting')

        // Convert only what fits within limits
        const canConvert = Math.max(0, 35 - currentVoters)
        const toConvert = deprecatedUsers.slice(0, canConvert)
        const toDeactivate = deprecatedUsers.slice(canConvert)

        // Convert some to voters
        if (toConvert.length > 0) {
          await User.updateMany({ _id: { $in: toConvert.map(u => u._id) } }, { role: 'voter' })
          logger.info(`Converted ${toConvert.length} users to voter role`)
        }

        // Deactivate the rest
        if (toDeactivate.length > 0) {
          await User.updateMany({ _id: { $in: toDeactivate.map(u => u._id) } }, { isActive: false })
          logger.info(`Deactivated ${toDeactivate.length} users due to account limits`)
        }
      } else {
        // Convert all to voters
        await User.updateMany({ role: { $in: ['execom', 'tie-breaker'] } }, { role: 'voter' })
        logger.info(`Converted ${deprecatedUsers.length} users to voter role`)
      }
    } else {
      // Option 2: Deactivate users with deprecated roles
      await User.updateMany({ role: { $in: ['execom', 'tie-breaker'] } }, { isActive: false })
      logger.info(`Deactivated ${deprecatedUsers.length} users with deprecated roles`)
    }

    // Verify final state
    const finalStats = await User.getAccountStats()
    logger.info('Final account statistics:')
    logger.info(`- Voters: ${finalStats.voters.current}/${finalStats.voters.maximum}`)
    logger.info(`- Admins: ${finalStats.admins.current}/${finalStats.admins.maximum}`)
    logger.info(`- Total active: ${finalStats.total}`)

    // Check for any remaining deprecated roles
    const remainingDeprecated = await User.countDocuments({
      role: { $in: ['execom', 'tie-breaker'] },
      isActive: true,
    })

    if (remainingDeprecated > 0) {
      logger.warn(`Warning: ${remainingDeprecated} active users still have deprecated roles`)
    } else {
      logger.info('✅ All deprecated roles have been cleaned up successfully')
    }
  } catch (error) {
    logger.error('Error during role cleanup:', error)
    throw error
  } finally {
    await mongoose.disconnect()
    logger.info('Disconnected from MongoDB')
  }
}

/**
 * Script to validate current account limits
 */
async function validateAccountLimits() {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Validating account limits...')

    const stats = await User.getAccountStats()

    logger.info('Current account status:')
    logger.info(
      `- Voters: ${stats.voters.current}/${stats.voters.maximum} (${stats.voters.remaining} remaining)`
    )
    logger.info(
      `- Admins: ${stats.admins.current}/${stats.admins.maximum} (${stats.admins.remaining} remaining)`
    )

    if (stats.voters.current > stats.voters.maximum) {
      logger.error(`❌ Voter limit exceeded: ${stats.voters.current}/${stats.voters.maximum}`)
    } else {
      logger.info(`✅ Voter limit OK: ${stats.voters.current}/${stats.voters.maximum}`)
    }

    if (stats.admins.current > stats.admins.maximum) {
      logger.error(`❌ Admin limit exceeded: ${stats.admins.current}/${stats.admins.maximum}`)
    } else {
      logger.info(`✅ Admin limit OK: ${stats.admins.current}/${stats.admins.maximum}`)
    }
  } catch (error) {
    logger.error('Error validating account limits:', error)
    throw error
  } finally {
    await mongoose.disconnect()
  }
}

// Run the appropriate script based on command line argument
const command = process.argv[2]

if (command === 'cleanup') {
  cleanupDeprecatedRoles()
    .then(() => {
      logger.info('Role cleanup completed successfully')
      process.exit(0)
    })
    .catch(error => {
      logger.error('Role cleanup failed:', error)
      process.exit(1)
    })
} else if (command === 'validate') {
  validateAccountLimits()
    .then(() => {
      logger.info('Account limit validation completed')
      process.exit(0)
    })
    .catch(error => {
      logger.error('Account limit validation failed:', error)
      process.exit(1)
    })
} else {
  console.log('Usage:')
  console.log('  node src/scripts/cleanupDeprecatedRoles.js cleanup   - Clean up deprecated roles')
  console.log('  node src/scripts/cleanupDeprecatedRoles.js validate  - Validate account limits')
  process.exit(1)
}
