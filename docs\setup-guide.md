# DFPTA E-Voting System Setup Guide

## Prerequisites

Before setting up the project, ensure you have the following installed:

- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **MongoDB** (v6 or higher) - [Download here](https://www.mongodb.com/try/download/community)
- **Git** - [Download here](https://git-scm.com/)
- **npm** or **yarn** package manager

## Quick Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd dfpta
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Edit the .env file with your configuration
# Required changes:
# - MONGODB_URI: Your MongoDB connection string
# - JWT_SECRET: A secure random string
# - JWT_REFRESH_SECRET: Another secure random string

# Start the backend server
npm run dev
```

The backend will be available at `http://localhost:5000`

### 3. Frontend Setup

```bash
# Navigate to frontend directory (from project root)
cd frontend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Edit the .env file if needed (defaults should work for development)

# Start the frontend development server
npm run dev
```

The frontend will be available at `http://localhost:3000`

## Environment Configuration

### Backend Environment Variables

Create a `.env` file in the `backend` directory with the following variables:

```env
# Environment
NODE_ENV=development

# Server Configuration
PORT=5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/dfpta_voting

# JWT Configuration (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-refresh-secret-key-change-this-in-production
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d

# Security Configuration
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
```

### Frontend Environment Variables

Create a `.env` file in the `frontend` directory:

```env
# API Configuration
VITE_API_URL=http://localhost:5000/api

# App Configuration
VITE_APP_NAME=DFPTA E-Voting System
VITE_APP_VERSION=2.0.0
```

## Database Setup

### MongoDB Installation

1. **Install MongoDB Community Edition**
   - Windows: Download from MongoDB website
   - macOS: `brew install mongodb-community`
   - Linux: Follow MongoDB documentation for your distribution

2. **Start MongoDB Service**
   ```bash
   # Windows (if installed as service)
   net start MongoDB
   
   # macOS
   brew services start mongodb-community
   
   # Linux
   sudo systemctl start mongod
   ```

3. **Verify MongoDB is Running**
   ```bash
   mongosh
   # Should connect to MongoDB shell
   ```

### Database Migration

Once the backend is running, you can migrate data from the existing MySQL database:

```bash
cd backend
npm run migrate
```

## Development Workflow

### Running Both Servers

You can run both frontend and backend simultaneously:

```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### Available Scripts

#### Backend Scripts
- `npm run dev` - Start development server with hot reload
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

#### Frontend Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## API Documentation

When running in development mode, API documentation is available at:
`http://localhost:5000/api/docs`

## Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running
   - Check the MONGODB_URI in your .env file
   - Verify MongoDB is accessible on the specified port

2. **Port Already in Use**
   - Change the PORT in backend .env file
   - Update VITE_API_URL in frontend .env file accordingly

3. **CORS Errors**
   - Ensure FRONTEND_URL in backend .env matches your frontend URL
   - Check that both servers are running

4. **Module Not Found Errors**
   - Run `npm install` in the respective directory
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`

### Getting Help

- Check the console logs for detailed error messages
- Ensure all environment variables are properly set
- Verify that all dependencies are installed
- Check that MongoDB is running and accessible

## Next Steps

After successful setup:

1. **Phase 2**: Set up MongoDB schemas and migration scripts
2. **Phase 3**: Implement authentication system
3. **Phase 4**: Develop core API endpoints
4. **Phase 5**: Build frontend components
5. **Phase 6**: Add advanced features
6. **Phase 7**: Testing and quality assurance
7. **Phase 8**: Documentation and deployment

## Security Notes

- **Never commit .env files** to version control
- **Change default JWT secrets** in production
- **Use strong passwords** for database connections
- **Enable MongoDB authentication** in production
- **Use HTTPS** in production environments
