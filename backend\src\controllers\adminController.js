import { validationResult } from 'express-validator'
import mongoose from 'mongoose'
import Candidate from '../models/Candidate.js'
import SystemSettings from '../models/SystemSettings.js'
import User from '../models/User.js'
import Vote from '../models/Vote.js'
import VoteReset from '../models/VoteReset.js'
import logger from '../utils/logger.js'

/**
 * @desc    Get all users with pagination and filtering
 * @route   GET /api/admin/users
 * @access  Private/Admin
 */
export const getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      role,
      municipality,
      hasVoted,
    } = req.query

    // Build filter object
    const filter = {}

    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { municipality: { $regex: search, $options: 'i' } },
      ]
    }

    if (role) filter.role = role
    if (municipality) filter.municipality = municipality
    if (hasVoted !== undefined) filter.hasVoted = hasVoted === 'true'

    // Build sort object
    const sortObj = {}
    sortObj[sort] = order === 'desc' ? -1 : 1

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit)

    // Execute queries
    const [users, total] = await Promise.all([
      User.find(filter).sort(sortObj).skip(skip).limit(parseInt(limit)).select('-refreshTokens'),
      User.countDocuments(filter),
    ])

    // Calculate pagination info
    const totalPages = Math.ceil(total / parseInt(limit))
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    res.status(200).json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalUsers: total,
          hasNextPage,
          hasPrevPage,
          limit: parseInt(limit),
        },
      },
    })
  } catch (error) {
    logger.error('Get users error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving users',
    })
  }
}

/**
 * @desc    Get user by ID
 * @route   GET /api/admin/users/:id
 * @access  Private/Admin
 */
export const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-refreshTokens')

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    res.status(200).json({
      success: true,
      data: { user },
    })
  } catch (error) {
    logger.error('Get user by ID error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving user',
    })
  }
}

/**
 * @desc    Create new user
 * @route   POST /api/admin/users
 * @access  Private/Admin
 */
export const createUser = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { username, municipality, password, role, email } = req.body
    const userRole = role || 'voter'

    // Check account limits before creating user
    try {
      await User.checkAccountLimits(userRole)
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: error.message,
      })
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { municipality }],
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error:
          existingUser.username === username
            ? 'Username already exists'
            : 'Municipality already has a user',
      })
    }

    // Create user
    const user = new User({
      username,
      municipality,
      password,
      role: userRole,
      email,
    })

    await user.save()

    logger.info(`Admin ${req.user.username} created user ${user.username}`)

    res.status(201).json({
      success: true,
      data: { user },
      message: 'User created successfully',
    })
  } catch (error) {
    logger.error('Create user error:', error)

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'User with this username or municipality already exists',
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error creating user',
    })
  }
}

/**
 * @desc    Update user
 * @route   PUT /api/admin/users/:id
 * @access  Private/Admin
 */
export const updateUser = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { municipality, role, email, isActive } = req.body

    const user = await User.findById(req.params.id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Prevent admin from deactivating themselves
    if (user._id.toString() === req.user.id && isActive === false) {
      return res.status(400).json({
        success: false,
        error: 'Cannot deactivate your own account',
      })
    }

    // Update fields
    if (municipality) user.municipality = municipality
    if (role) user.role = role
    if (email !== undefined) user.email = email
    if (isActive !== undefined) user.isActive = isActive

    await user.save()

    logger.info(`Admin ${req.user.username} updated user ${user.username}`)

    res.status(200).json({
      success: true,
      data: { user },
      message: 'User updated successfully',
    })
  } catch (error) {
    logger.error('Update user error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error updating user',
    })
  }
}

/**
 * @desc    Delete user
 * @route   DELETE /api/admin/users/:id
 * @access  Private/Admin
 */
export const deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Prevent admin from deleting themselves
    if (user._id.toString() === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account',
      })
    }

    await User.findByIdAndDelete(req.params.id)

    logger.info(`Admin ${req.user.username} deleted user ${user.username}`)

    res.status(200).json({
      success: true,
      message: 'User deleted successfully',
    })
  } catch (error) {
    logger.error('Delete user error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error deleting user',
    })
  }
}

/**
 * @desc    Get dashboard statistics
 * @route   GET /api/admin/dashboard
 * @access  Private/Admin
 */
export const getDashboardStats = async (req, res) => {
  try {
    const [totalUsers, totalVoters, votedUsers, activeUsers, usersByRole, recentUsers] =
      await Promise.all([
        User.countDocuments(),
        User.countDocuments({ role: 'voter' }),
        User.countDocuments({ hasVoted: true }),
        User.countDocuments({ isActive: true }),
        User.aggregate([{ $group: { _id: '$role', count: { $sum: 1 } } }]),
        User.find({ isActive: true })
          .sort({ createdAt: -1 })
          .limit(5)
          .select('username municipality role createdAt'),
      ])

    const votingProgress = totalVoters > 0 ? (votedUsers / totalVoters) * 100 : 0

    res.status(200).json({
      success: true,
      data: {
        stats: {
          totalUsers,
          totalVoters,
          votedUsers,
          activeUsers,
          votingProgress: Math.round(votingProgress * 100) / 100,
        },
        usersByRole: usersByRole.reduce((acc, item) => {
          acc[item._id] = item.count
          return acc
        }, {}),
        recentUsers,
      },
    })
  } catch (error) {
    logger.error('Get dashboard stats error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving dashboard statistics',
    })
  }
}

/**
 * @desc    Reset user's vote
 * @route   POST /api/admin/users/:id/reset-vote
 * @access  Private/Admin
 */
export const resetUserVote = async (req, res) => {
  try {
    const { reason = 'Admin reset' } = req.body
    const user = await User.findById(req.params.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Find all votes by this user to backup before reset
    const userVotes = await Vote.find({ voterId: user._id })

    if (userVotes.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'User has no votes to reset',
      })
    }

    // Create backups for all votes before deletion
    const backupPromises = userVotes.map(vote =>
      VoteBackup.createBackup(
        vote,
        'reset',
        `Vote reset by admin: ${reason}`,
        req.user.id,
        req.ip,
        req.get('User-Agent')
      )
    )

    await Promise.all(backupPromises)

    // Get candidates that will have their vote counts reduced
    const candidateIds = [...new Set(userVotes.map(vote => vote.candidateId))]
    const candidates = await Candidate.find({ _id: { $in: candidateIds } })

    // Reduce candidate vote counts
    for (const vote of userVotes) {
      const candidate = candidates.find(c => c._id.toString() === vote.candidateId.toString())
      if (candidate) {
        candidate.totalVotes = Math.max(0, candidate.totalVotes - 1)
        await candidate.save()
      }
    }

    // Delete the votes
    await Vote.deleteMany({ voterId: user._id })

    // Reset user voting status
    user.hasVoted = false
    user.votingRounds.hasVotedInRound = {
      round1: false,
      round2: false,
      round3: false,
    }
    user.votingRounds.lastVotedRound = null
    user.votingRounds.lastVotedAt = null
    await user.save()

    // Log activity
    await ActivityLog.logActivity({
      activityType: 'vote_reset',
      performedBy: req.user.id,
      targetUser: user._id,
      description: `Admin ${req.user.username} reset ${userVotes.length} votes for user ${user.username}`,
      metadata: {
        resetReason: reason,
        votesReset: userVotes.length,
        candidatesAffected: candidateIds.length,
        adminMunicipality: req.user.municipality,
        targetMunicipality: user.municipality,
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      requestMethod: req.method,
      requestUrl: req.originalUrl,
      responseStatus: 200,
      severity: 'high',
      category: 'administration',
    })

    logger.info(
      `Admin ${req.user.username} reset ${userVotes.length} votes for user ${user.username}`
    )

    res.status(200).json({
      success: true,
      message: 'User votes reset successfully',
      data: {
        votesReset: userVotes.length,
        candidatesAffected: candidateIds.length,
        backupsCreated: userVotes.length,
      },
    })
  } catch (error) {
    logger.error('Reset user vote error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error resetting user vote',
    })
  }
}

/**
 * @desc    Bulk operations on users
 * @route   POST /api/admin/users/bulk
 * @access  Private/Admin
 */
export const bulkUserOperations = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { userIds, action } = req.body

    // Prevent admin from performing bulk operations on themselves
    if (userIds.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot perform bulk operations on your own account',
      })
    }

    let updateQuery = {}
    let message = ''

    switch (action) {
      case 'activate':
        updateQuery = { isActive: true }
        message = 'Users activated successfully'
        break
      case 'deactivate':
        updateQuery = { isActive: false }
        message = 'Users deactivated successfully'
        break
      case 'reset-votes':
        updateQuery = { hasVoted: false }
        message = 'User votes reset successfully'
        break
      case 'delete':
        await User.deleteMany({ _id: { $in: userIds } })
        logger.info(`Admin ${req.user.username} bulk deleted ${userIds.length} users`)
        return res.status(200).json({
          success: true,
          message: 'Users deleted successfully',
        })
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid bulk action',
        })
    }

    const result = await User.updateMany({ _id: { $in: userIds } }, updateQuery)

    logger.info(
      `Admin ${req.user.username} performed bulk ${action} on ${result.modifiedCount} users`
    )

    res.status(200).json({
      success: true,
      message,
      data: {
        modifiedCount: result.modifiedCount,
      },
    })
  } catch (error) {
    logger.error('Bulk user operations error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error performing bulk operations',
    })
  }
}

/**
 * @desc    Get admin voting results with unified ranking and toggle functionality
 * @route   GET /api/admin/results
 * @access  Private/Admin
 */
export const getAdminResults = async (req, res) => {
  try {
    const { showNames = 'false', sortBy = 'votes', viewMode = 'unified' } = req.query
    const showCandidateNames = showNames === 'true'

    // Get all candidates with their vote counts
    const candidates = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes')
      .sort({ totalVotes: -1, municipalityName: 1 })

    // Create unified ranking (1-35 based on total votes)
    const unifiedResults = candidates.map((candidate, index) => ({
      municipalityName: candidate.municipalityName,
      district: candidate.district,
      voteCount: candidate.totalVotes,
      rank: index + 1, // Overall rank from 1-35
      candidateName: showCandidateNames ? candidate.municipalityName : undefined,
    }))

    // Group results by district (for legacy compatibility)
    const resultsByDistrict = candidates.reduce((acc, candidate) => {
      const district = candidate.district
      if (!acc[district]) {
        acc[district] = []
      }

      const result = {
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        voteCount: candidate.totalVotes,
        rank: 0, // Will be calculated per district
      }

      // Add candidate name if toggle is enabled
      if (showCandidateNames) {
        result.candidateName = candidate.municipalityName
      }

      acc[district].push(result)
      return acc
    }, {})

    // Calculate ranks within each district and sort
    Object.keys(resultsByDistrict).forEach(district => {
      resultsByDistrict[district] = resultsByDistrict[district]
        .sort((a, b) => {
          if (sortBy === 'votes') {
            return b.voteCount - a.voteCount
          }
          return a.municipalityName.localeCompare(b.municipalityName)
        })
        .map((candidate, index) => ({
          ...candidate,
          rank: index + 1,
        }))
    })

    // Get overall statistics
    const totalVoters = await User.countDocuments({ role: 'voter', isActive: true })
    const totalVotesCast = await User.countDocuments({
      role: 'voter',
      hasVoted: true,
      isActive: true,
    })
    const totalVotes = candidates.reduce((sum, candidate) => sum + candidate.totalVotes, 0)
    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0

    const totalStats = {
      totalVotes,
      totalVoters,
      totalVotesCast,
      participationRate: Math.round(participationRate * 100) / 100,
      totalCandidates: candidates.length,
    }

    res.status(200).json({
      success: true,
      data: {
        resultsByDistrict,
        unifiedResults, // New unified ranking (1-35)
        totalStats,
        showCandidateNames,
        sortBy,
        viewMode,
      },
    })
  } catch (error) {
    logger.error('Get admin results error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving voting results',
    })
  }
}

/**
 * @desc    Get activity logs with filtering and pagination
 * @route   GET /api/admin/activity-logs
 * @access  Private/Admin
 */
export const getActivityLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      activityType,
      category,
      severity,
      isSecurityEvent,
      startDate,
      endDate,
      userId,
    } = req.query

    const filters = {}
    if (activityType) filters.activityType = activityType
    if (category) filters.category = category
    if (severity) filters.severity = severity
    if (isSecurityEvent !== undefined) filters.isSecurityEvent = isSecurityEvent === 'true'
    if (userId) filters.performedBy = userId
    if (startDate || endDate) {
      filters.startDate = startDate
      filters.endDate = endDate
    }

    const activities = await ActivityLog.getRecentActivities(parseInt(limit), filters)
    const stats = await ActivityLog.getActivityStats('24h')

    res.status(200).json({
      success: true,
      data: {
        activities,
        stats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: activities.length,
        },
      },
    })
  } catch (error) {
    logger.error('Get activity logs error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching activity logs',
    })
  }
}

/**
 * @desc    Get vote backups with filtering and pagination
 * @route   GET /api/admin/vote-backups
 * @access  Private/Admin
 */
export const getVoteBackups = async (req, res) => {
  try {
    const { page = 1, limit = 50, backupType, userId, startDate, endDate } = req.query

    const query = {}
    if (backupType) query.backupType = backupType
    if (userId) query['originalVoteData.voterId'] = userId
    if (startDate || endDate) {
      query.backupTimestamp = {}
      if (startDate) query.backupTimestamp.$gte = new Date(startDate)
      if (endDate) query.backupTimestamp.$lte = new Date(endDate)
    }

    const backups = await VoteBackup.find(query)
      .populate('performedBy', 'username municipality role')
      .populate('originalVoteData.voterId', 'username municipality')
      .populate('originalVoteData.candidateId', 'municipalityName district')
      .sort({ backupTimestamp: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))

    const totalBackups = await VoteBackup.countDocuments(query)
    const stats = await VoteBackup.getBackupStats()

    res.status(200).json({
      success: true,
      data: {
        backups,
        stats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalBackups,
          pages: Math.ceil(totalBackups / parseInt(limit)),
        },
      },
    })
  } catch (error) {
    logger.error('Get vote backups error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching vote backups',
    })
  }
}

/**
 * @desc    Restore vote from backup
 * @route   POST /api/admin/vote-backups/:id/restore
 * @access  Private/Admin
 */
export const restoreVoteBackup = async (req, res) => {
  try {
    const { restorationNotes = '' } = req.body
    const backup = await VoteBackup.findById(req.params.id)

    if (!backup) {
      return res.status(404).json({
        success: false,
        error: 'Vote backup not found',
      })
    }

    if (backup.isRestored) {
      return res.status(400).json({
        success: false,
        error: 'Vote backup has already been restored',
      })
    }

    await backup.restore(req.user.id, restorationNotes)

    // Log activity
    await ActivityLog.logActivity({
      activityType: 'admin_action',
      performedBy: req.user.id,
      description: `Admin ${req.user.username} restored vote backup`,
      metadata: {
        backupId: backup._id,
        originalVoteId: backup.originalVoteId,
        backupType: backup.backupType,
        restorationNotes,
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      requestMethod: req.method,
      requestUrl: req.originalUrl,
      responseStatus: 200,
      severity: 'high',
      category: 'administration',
    })

    logger.info(`Admin ${req.user.username} restored vote backup ${backup._id}`)

    res.status(200).json({
      success: true,
      message: 'Vote backup restored successfully',
      data: {
        backup,
      },
    })
  } catch (error) {
    logger.error('Restore vote backup error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error restoring vote backup',
    })
  }
}

/**
 * @desc    Get account statistics and limits
 * @route   GET /api/admin/account-stats
 * @access  Private/Admin
 */
export const getAccountStats = async (req, res) => {
  try {
    const stats = await User.getAccountStats()

    res.status(200).json({
      success: true,
      data: stats,
    })
  } catch (error) {
    logger.error('Get account stats error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching account statistics',
    })
  }
}

/**
 * @desc    Reset all votes in the system
 * @route   POST /api/admin/reset-votes
 * @access  Private/Admin
 */
export const resetAllVotes = async (req, res) => {
  const session = await mongoose.startSession()

  try {
    await session.withTransaction(async () => {
      const { reason } = req.body
      const adminId = req.user.id
      const adminUsername = req.user.username
      const ipAddress = req.ip || req.connection.remoteAddress
      const userAgent = req.get('User-Agent') || 'Unknown'

      // Get pre-reset statistics
      const preResetStats = {
        totalVotes: await Vote.countDocuments(),
        totalVoters: await User.countDocuments({ role: 'voter', isActive: true }),
        votedUsersCount: await User.countDocuments({ hasVoted: true, isActive: true }),
      }

      // Create vote reset record
      const voteReset = new VoteReset({
        adminId,
        adminUsername,
        resetType: 'all-votes',
        affectedUsersCount: preResetStats.votedUsersCount,
        affectedVotesCount: preResetStats.totalVotes,
        reason,
        ipAddress,
        userAgent,
        preResetStats,
        status: 'pending',
      })

      await voteReset.save({ session })

      try {
        // Reset all votes
        await Vote.deleteMany({}, { session })

        // Reset user voting status
        await User.updateMany(
          { hasVoted: true },
          {
            hasVoted: false,
            lastVotedAt: null,
          },
          { session }
        )

        // Get post-reset statistics
        const postResetStats = {
          totalVotes: await Vote.countDocuments(),
          totalVoters: await User.countDocuments({ role: 'voter', isActive: true }),
          votedUsersCount: await User.countDocuments({ hasVoted: true, isActive: true }),
        }

        // Mark reset as completed
        await voteReset.markCompleted(postResetStats)

        logger.info(
          `Admin ${adminUsername} reset all votes. Affected: ${preResetStats.totalVotes} votes, ${preResetStats.votedUsersCount} users`
        )

        res.status(200).json({
          success: true,
          message: 'All votes have been reset successfully',
          data: {
            resetId: voteReset._id,
            affectedVotes: preResetStats.totalVotes,
            affectedUsers: preResetStats.votedUsersCount,
            resetAt: new Date(),
          },
        })
      } catch (resetError) {
        // Mark reset as failed
        await voteReset.markFailed(resetError.message)
        throw resetError
      }
    })
  } catch (error) {
    logger.error('Reset all votes error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error resetting votes',
    })
  } finally {
    await session.endSession()
  }
}

/**
 * @desc    Get vote reset audit logs
 * @route   GET /api/admin/vote-resets
 * @access  Private/Admin
 */
export const getVoteResets = async (req, res) => {
  try {
    const { page = 1, limit = 20, resetType, status, adminId, startDate, endDate } = req.query

    // Build filter conditions
    const filterConditions = {}

    if (resetType) {
      filterConditions.resetType = resetType
    }

    if (status) {
      filterConditions.status = status
    }

    if (adminId) {
      filterConditions.adminId = adminId
    }

    if (startDate || endDate) {
      filterConditions.createdAt = {}
      if (startDate) {
        filterConditions.createdAt.$gte = new Date(startDate)
      }
      if (endDate) {
        filterConditions.createdAt.$lte = new Date(endDate)
      }
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit)

    // Get vote resets with pagination
    const voteResets = await VoteReset.find(filterConditions)
      .populate('adminId', 'username municipality')
      .populate('targetUserId', 'username municipality')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    // Get total count for pagination
    const totalCount = await VoteReset.countDocuments(filterConditions)

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / parseInt(limit))
    const hasNextPage = parseInt(page) < totalPages
    const hasPrevPage = parseInt(page) > 1

    res.status(200).json({
      success: true,
      data: {
        voteResets,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: parseInt(limit),
        },
      },
    })
  } catch (error) {
    logger.error('Get vote resets error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching vote reset logs',
    })
  }
}

/**
 * @desc    Get vote reset statistics
 * @route   GET /api/admin/vote-reset-stats
 * @access  Private/Admin
 */
export const getVoteResetStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query

    const stats = await VoteReset.getResetStatistics(startDate, endDate)

    res.status(200).json({
      success: true,
      data: stats[0] || {
        totalResets: 0,
        totalVotesReset: 0,
        totalUsersAffected: 0,
        resetsByType: [],
        resetsByStatus: [],
        adminActivity: [],
      },
    })
  } catch (error) {
    logger.error('Get vote reset stats error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching vote reset statistics',
    })
  }
}

/**
 * @desc    Toggle user presence status
 * @route   PATCH /api/admin/users/:id/presence
 * @access  Private/Admin
 */
export const toggleUserPresence = async (req, res) => {
  try {
    const { id } = req.params
    const { isPresent } = req.body

    // Find the user
    const user = await User.findById(id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Update presence status
    user.isPresent = isPresent
    await user.save()

    logger.info(`Admin ${req.user.username} set user ${user.username} presence to ${isPresent}`)

    res.status(200).json({
      success: true,
      message: `User ${user.username} marked as ${isPresent ? 'present' : 'absent'}`,
      data: {
        userId: user._id,
        username: user.username,
        isPresent: user.isPresent,
      },
    })
  } catch (error) {
    logger.error('Toggle user presence error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error updating user presence',
    })
  }
}

/**
 * @desc    Bulk update user presence status
 * @route   PATCH /api/admin/users/bulk-presence
 * @access  Private/Admin
 */
export const bulkUpdatePresence = async (req, res) => {
  try {
    const { userIds, isPresent } = req.body

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'User IDs array is required',
      })
    }

    // Update multiple users
    const result = await User.updateMany({ _id: { $in: userIds } }, { isPresent })

    logger.info(
      `Admin ${req.user.username} bulk updated ${result.modifiedCount} users presence to ${isPresent}`
    )

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} users marked as ${isPresent ? 'present' : 'absent'}`,
      data: {
        modifiedCount: result.modifiedCount,
        isPresent,
      },
    })
  } catch (error) {
    logger.error('Bulk update presence error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error updating user presence',
    })
  }
}

/**
 * @desc    Get system settings
 * @route   GET /api/admin/settings
 * @access  Private/Admin
 */
export const getSystemSettings = async (req, res) => {
  try {
    const { category } = req.query

    let settings
    if (category) {
      settings = await SystemSettings.getSettingsByCategory(category)
    } else {
      settings = await SystemSettings.getEditableSettings()
    }

    res.status(200).json({
      success: true,
      data: settings,
    })
  } catch (error) {
    logger.error('Get system settings error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching system settings',
    })
  }
}

/**
 * @desc    Update system setting
 * @route   PATCH /api/admin/settings/:key
 * @access  Private/Admin
 */
export const updateSystemSetting = async (req, res) => {
  try {
    const { key } = req.params
    const { value } = req.body
    const adminId = req.user.id

    const setting = await SystemSettings.setSetting(key, value, adminId)

    logger.info(`Admin ${req.user.username} updated setting ${key} to ${value}`)

    res.status(200).json({
      success: true,
      message: `Setting ${key} updated successfully`,
      data: setting,
    })
  } catch (error) {
    logger.error('Update system setting error:', error)

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: error.message,
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error updating system setting',
    })
  }
}

/**
 * @desc    Toggle public results visibility
 * @route   PATCH /api/admin/toggle-public-results
 * @access  Private/Admin
 */
export const togglePublicResults = async (req, res) => {
  try {
    const { enabled } = req.body
    const adminId = req.user.id

    const setting = await SystemSettings.setSetting('public_results_enabled', enabled, adminId)

    logger.info(`Admin ${req.user.username} ${enabled ? 'enabled' : 'disabled'} public results`)

    res.status(200).json({
      success: true,
      message: `Public results ${enabled ? 'enabled' : 'disabled'} successfully`,
      data: {
        publicResultsEnabled: setting.value,
        lastModifiedBy: req.user.username,
        lastModifiedAt: setting.lastModifiedAt,
      },
    })
  } catch (error) {
    logger.error('Toggle public results error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error toggling public results',
    })
  }
}
