import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Printer, 
  Loader2,
  CheckCircle 
} from 'lucide-react'

interface ExportResultsProps {
  resultsByDistrict: Record<string, any[]> | null
  totalStats: any
  className?: string
}

export function ExportResults({ resultsByDistrict, totalStats, className }: ExportResultsProps) {
  const [exportFormat, setExportFormat] = useState<'csv' | 'pdf' | 'json'>('csv')
  const [exportScope, setExportScope] = useState<'all' | 'district'>('all')
  const [selectedDistrict, setSelectedDistrict] = useState<string>('')
  const [isExporting, setIsExporting] = useState(false)
  const [exportSuccess, setExportSuccess] = useState(false)

  const districts = resultsByDistrict ? Object.keys(resultsByDistrict) : []

  const handleExport = async () => {
    setIsExporting(true)
    setExportSuccess(false)

    try {
      // Prepare data based on scope
      let dataToExport = resultsByDistrict
      if (exportScope === 'district' && selectedDistrict) {
        dataToExport = { [selectedDistrict]: resultsByDistrict?.[selectedDistrict] || [] }
      }

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000))

      switch (exportFormat) {
        case 'csv':
          exportToCSV(dataToExport, totalStats)
          break
        case 'pdf':
          exportToPDF(dataToExport, totalStats)
          break
        case 'json':
          exportToJSON(dataToExport, totalStats)
          break
      }

      setExportSuccess(true)
      setTimeout(() => setExportSuccess(false), 3000)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const exportToCSV = (data: any, stats: any) => {
    const csvContent = generateCSVContent(data, stats)
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `voting-results-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const exportToPDF = (data: any, stats: any) => {
    // In a real implementation, you would use a library like jsPDF
    console.log('Exporting to PDF:', data, stats)
    // For now, just create a simple text file
    const content = generateTextContent(data, stats)
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `voting-results-${new Date().toISOString().split('T')[0]}.txt`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const exportToJSON = (data: any, stats: any) => {
    const jsonContent = JSON.stringify({ results: data, statistics: stats }, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `voting-results-${new Date().toISOString().split('T')[0]}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const generateCSVContent = (data: any, stats: any) => {
    let csv = 'District,Municipality,Rank,Vote Count\n'
    
    if (data) {
      Object.entries(data).forEach(([district, candidates]: [string, any[]]) => {
        candidates.forEach(candidate => {
          csv += `"${district}","${candidate.municipalityName}",${candidate.rank},${candidate.voteCount}\n`
        })
      })
    }

    csv += '\n\nStatistics\n'
    csv += `Total Votes,${stats?.totalVotes || 0}\n`
    csv += `Total Voters,${stats?.totalVoters || 0}\n`
    csv += `Participation Rate,${stats?.participationRate || 0}%\n`
    csv += `Total Candidates,${stats?.totalCandidates || 0}\n`

    return csv
  }

  const generateTextContent = (data: any, stats: any) => {
    let content = 'DFPTA E-Voting System - Results Report\n'
    content += `Generated on: ${new Date().toLocaleString()}\n\n`

    content += 'STATISTICS\n'
    content += '==========\n'
    content += `Total Votes: ${stats?.totalVotes || 0}\n`
    content += `Total Voters: ${stats?.totalVoters || 0}\n`
    content += `Participation Rate: ${stats?.participationRate || 0}%\n`
    content += `Total Candidates: ${stats?.totalCandidates || 0}\n\n`

    content += 'RESULTS BY DISTRICT\n'
    content += '==================\n\n'

    if (data) {
      Object.entries(data).forEach(([district, candidates]: [string, any[]]) => {
        content += `${district}\n`
        content += '-'.repeat(district.length) + '\n'
        candidates.forEach(candidate => {
          content += `${candidate.rank}. ${candidate.municipalityName} - ${candidate.voteCount} votes\n`
        })
        content += '\n'
      })
    }

    return content
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <FileSpreadsheet className="h-4 w-4" />
      case 'pdf':
        return <FileText className="h-4 w-4" />
      case 'json':
        return <FileText className="h-4 w-4" />
      default:
        return <Download className="h-4 w-4" />
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          Export Results
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Export Format */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Export Format</label>
          <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="csv">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  CSV (Excel Compatible)
                </div>
              </SelectItem>
              <SelectItem value="pdf">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  PDF Report
                </div>
              </SelectItem>
              <SelectItem value="json">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  JSON Data
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Export Scope */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Export Scope</label>
          <Select value={exportScope} onValueChange={(value: any) => setExportScope(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Districts</SelectItem>
              <SelectItem value="district">Specific District</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* District Selection */}
        {exportScope === 'district' && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Select District</label>
            <Select value={selectedDistrict} onValueChange={setSelectedDistrict}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a district" />
              </SelectTrigger>
              <SelectContent>
                {districts.map(district => (
                  <SelectItem key={district} value={district}>
                    {district}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Export Button */}
        <Button 
          onClick={handleExport} 
          disabled={isExporting || (exportScope === 'district' && !selectedDistrict)}
          className="w-full"
        >
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Exporting...
            </>
          ) : exportSuccess ? (
            <>
              <CheckCircle className="mr-2 h-4 w-4" />
              Exported Successfully!
            </>
          ) : (
            <>
              {getFormatIcon(exportFormat)}
              <span className="ml-2">Export {exportFormat.toUpperCase()}</span>
            </>
          )}
        </Button>

        {/* Export Info */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• CSV files can be opened in Excel or Google Sheets</p>
          <p>• PDF reports include formatted tables and statistics</p>
          <p>• JSON files contain raw data for developers</p>
        </div>
      </CardContent>
    </Card>
  )
}
