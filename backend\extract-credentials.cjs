const fs = require('fs');

try {
  const credentials = JSON.parse(fs.readFileSync('credentials-output.json', 'utf8'));

  console.log('=== 1ST DISTRICT ===');
  credentials.municipalities.filter(m => m.district === '1st District').forEach(m => {
    console.log(`| ${m.municipality} | \`${m.username}\` | ${m.district} | \`${m.password}\` |`);
  });

  console.log('\n=== 2ND DISTRICT ===');
  credentials.municipalities.filter(m => m.district === '2nd District').forEach(m => {
    console.log(`| ${m.municipality} | \`${m.username}\` | ${m.district} | \`${m.password}\` |`);
  });

  console.log('\n=== 3RD DISTRICT ===');
  credentials.municipalities.filter(m => m.district === '3rd District').forEach(m => {
    console.log(`| ${m.municipality} | \`${m.username}\` | ${m.district} | \`${m.password}\` |`);
  });

  console.log('\n=== 4TH DISTRICT ===');
  credentials.municipalities.filter(m => m.district === '4th District').forEach(m => {
    console.log(`| ${m.municipality} | \`${m.username}\` | ${m.district} | \`${m.password}\` |`);
  });

  console.log('\n=== 5TH DISTRICT ===');
  credentials.municipalities.filter(m => m.district === '5th District').forEach(m => {
    console.log(`| ${m.municipality} | \`${m.username}\` | ${m.district} | \`${m.password}\` |`);
  });

  console.log('\n=== EXECOM ===');
  credentials.execom.forEach(e => {
    console.log(`| ${e.username} | ${e.municipality} | ${e.district} | \`${e.password}\` |`);
  });

  console.log('\n=== TIEBREAKER ===');
  console.log(`| ${credentials.tiebreaker.username} | ${credentials.tiebreaker.municipality} | ${credentials.tiebreaker.district} | \`${credentials.tiebreaker.password}\` |`);

} catch (error) {
  console.error('Error reading credentials:', error.message);
}
