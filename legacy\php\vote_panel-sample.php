<?php
include 'db_connection.php';

session_start();
$voter_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Check if voter_id is missing or empty
if ($voter_id === null || empty($voter_id)) {
    die("Error: Voter ID is missing or empty in the session.");
}

// Check if the user has already voted
$check_sql = "SELECT * FROM votes WHERE voter_id = ?";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->bind_param("i", $voter_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result === false) {
    die("Query failed: " . $conn->error);
}
?> 

<!DOCTYPE html>
<html>
<head>
    <title>DFPTA E-Voting System</title>
    <link rel="stylesheet" type="text/css" href="vote_panel-sample.css"> <!-- Link to your CSS file -->
    <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@400;700&display=swap" rel="stylesheet">
    <link rel="icon" href="dfptalogo.png" type="image/x-icon"> 
</head>

<header class="header">
    <div class="container-nav">
        <div class="logo-container">
            <img src="dfptalogo.png" alt="Online Voting System Logo" class="logo">
            <a href="index.html"><b>DFPTA E-VOTING SYSTEM</b></a>
        </div>
        <nav class="nav-center">
        </nav>
    </div>
</header>
   

<body>
    <section class="form-section" style="background-image: url('ff/bgg1.png');">
    <div class="form-container">
            <form action="vote.php" method="post">
            <?php
        if ($check_result->num_rows > 0) {
            echo '<div class="content">';

            // Count the number of voted candidates
            $count_sql = "SELECT COUNT(*) as count FROM votes WHERE voter_id = ?";
            $count_stmt = $conn->prepare($count_sql);
            $count_stmt->bind_param("i", $voter_id);
            $count_stmt->execute();
            $count_result = $count_stmt->get_result();
            $count_row = $count_result->fetch_assoc();
            $voted_count = $count_row['count'];

            echo "<p>You have voted for a total of $voted_count candidate(s).</p>";

            // Query the database to retrieve voted candidates using a prepared statement
            $query = "SELECT c.candidate_name FROM votes v
                    JOIN candidates c ON v.candidate_id = c.candidate_id
                    WHERE v.voter_id = ?";

            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $voter_id);
            $stmt->execute();
            $result = $stmt->get_result();

            // Check for SQL query execution error
            if (!$result) {
                die("Query failed: " . $conn->error);
            }

            echo "<ul>";
            while ($row = $result->fetch_assoc()) {
                echo "<li>{$row['candidate_name']}</li>";
            }
            echo "</ul>";

            // Display logout button
                // Display logout button
                    echo '<form action="login.html" method="post" style="margin-top: 50px;">'; // Adjust the margin-top value as needed
                    echo '<input type="submit" value="Logout" style="background-color: #243328; color: #fff; padding: 10px; border: none; border-radius: 5px; cursor: pointer;">';
                    echo '</form>';

            echo '</div>'; // Close the content div
            } 

        else {
                // User hasn't voted yet
                echo '<div class="content">';
                echo "<p>You haven't voted for any candidates yet.</p>";
                echo '</div>';
            }
    ?>
    </section>
</body>
</html>

  
