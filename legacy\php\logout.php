<?php
session_start(); // Start the session (if not already started)

// Check if the user is logged in (you can modify this check based on your authentication mechanism)
if (isset($_SESSION['user_id'])) {
    // Unset all of the session variables
    $_SESSION = array();

    // Destroy the session
    session_destroy();
    
    // Redirect to the login page or any other page you want
    header('Location: login.html'); // Change 'login.php' to your login page
    exit();
} else {
    $_SESSION['voted'] = false;

    // Redirect to the login page in case the user is not logged in
    header('Location: login.html'); // Change 'login.php' to your login page
    exit();
}
?>
