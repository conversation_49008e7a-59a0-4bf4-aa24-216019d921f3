# Legacy DFPTA E-Voting System Files

This directory contains the original PHP-based voting system files that are being migrated to the new MERN stack architecture.

## Directory Structure

```
legacy/
├── php/                    # PHP application files
├── html/                   # HTML templates and pages
├── css/                    # Stylesheets
└── assets/                 # Images, fonts, SQL, and other assets
```

## Key Files and Their Purpose

### PHP Files (Business Logic to Migrate)

#### Authentication & User Management
- `login.php` - Main login system with hardcoded municipality credentials
- `admin_login.php` - Admin authentication (admin/socmob)
- `execom_login.php` - Executive committee member login
- `tie_login.php` - Tie-breaker voting authentication
- `logout.php`, `admin_logout.php`, `exec_logout.php` - Logout handlers

#### Voting System
- `vote.php` - Main voting logic (up to 15 candidates)
- `execom_vote.php` - Executive committee voting
- `vote_chairperson.php` - Officer voting (chairperson)
- `tie_vote.php` - Tie-breaking vote handling
- `process_tie_vote.php` - Tie vote processing

#### Results & Administration
- `admin_panel.php` - Main admin dashboard
- `listfinal.php` - Final results display
- `top35-sample.php` - Top candidates listing
- `results.php` - Public results page
- `reset_votes.php` - Vote reset functionality

#### Officer Selection
- `chairperson.php` - Chairperson selection interface
- `vicechairperson.php` - Vice-chairperson selection
- `secretary.php` - Secretary selection
- `treasurer.php` - Treasurer selection
- `exec.php` - Executive committee management

#### Tie-Breaking System
- `tie_candidates.php` - Identify tied candidates
- `tie_panel.php` - Tie-breaking panel
- `tie_result.php` - Tie-breaking results

#### Database & Utilities
- `db_connection.php` - MySQL database connection
- `voting_system.sql` - Database schema and sample data

### HTML Files (UI Templates)
- `index.html` - Homepage
- `login.html` - Login form
- `admin_login.html` - Admin login form
- `vote.html` - Voting interface
- `contact.html` - Contact page
- Various dashboard and panel HTML files

### CSS Files (Styling)
- `login.css` - Login page styles
- `admin_panel.css` - Admin panel styles
- `vote.css` - Voting interface styles
- Various component-specific stylesheets

### Assets
- `dfptalogo.png` - DFPTA logo (to be moved to frontend)
- `fonts/` - RobotoSlab font family
- `ff/` - Background images and UI assets
- `voting_system.sql` - Complete database dump

## Migration Notes

### Database Schema (MySQL → MongoDB)
```sql
-- Original MySQL tables:
candidates (candidate_id, candidate_name)
votes (vote_id, voter_id, candidate_id)
tie_votes (vote_id, voter_id, candidate_id)
execom_members (member_id, candidate_id, candidate_name, total_votes)
chairperson (vote_id, voter_id, candidate_id)
voters (voter_id, candidate_name)
vote_result (no, candidate_name, candidate_id, total_votes)
```

### Authentication System
- **Current**: Hardcoded municipality names and passwords
- **Migration**: JWT-based authentication with proper user management
- **Users**: 35 municipalities with mapped IDs (1-35)

### Voting Logic
1. **Regular Voting**: Users select up to 15 candidates
2. **Top 15 Selection**: System determines top 15 based on votes
3. **Tie-Breaking**: Special voting for tied positions
4. **Officer Selection**: Executive committee votes for officers

### Key Business Rules
- Maximum 15 votes per voter
- One vote per voter (prevent double voting)
- Tie-breaking system for 15th position
- Officer positions: Chairperson, Vice-chairperson, Secretary, Treasurer
- Admin can reset all votes
- Real-time vote counting and results

## Files Requiring Special Attention

### Critical Business Logic
1. `login.php` - Municipality mapping and authentication
2. `vote.php` - Core voting logic and validation
3. `admin_panel.php` - Results calculation and display
4. `tie_vote.php` - Tie-breaking algorithm
5. `exec.php` - Executive committee selection

### Security Concerns (Fixed in New System)
- Hardcoded credentials
- No password hashing
- Basic session management
- No input validation
- No rate limiting

## Migration Status

- ✅ **Project Structure**: Organized and separated
- ⏳ **Database Schema**: To be converted to MongoDB
- ⏳ **Authentication**: To be implemented with JWT
- ⏳ **Voting Logic**: To be ported to Express.js
- ⏳ **Frontend**: To be rebuilt with React
- ⏳ **Admin Panel**: To be redesigned

## Reference Information

### Municipality List (35 total)
Baao, Balatan, Bato, Bombon, Buhi, Bula, Cabusao, Calabanga, Camaligan, Canaman, Caramoan, Del Gallego, Gainza, Garchitorena, Goa, Lagonoy, Libmanan, Lupi, Magarao, Milaor, Minalabac, Nabua, Ocampo, Pamplona, Pasacao, Parubcan, Pili, Ragay, Sagnay, San Fernando, San Jose, Sipocot, Siruma, Tigaon, Tinambac

### Admin Credentials (Legacy)
- Username: admin
- Password: socmob

**⚠️ Note**: These files are kept for reference during migration. Do not modify or use in production.
