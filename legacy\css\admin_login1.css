/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body {
    font-family: Arial, sans-serif;
    background-color: #f7f7f7;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin: 0;
}

.container {
    max-width: 400px; /* Set the maximum width as needed */
    margin: 0 auto; /* Center the container */
   /* outline: 2px solid #243328; */
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Header styles *//* Header styles */
.header {
    background: #fff;
    color: #333;
    padding: 0;
    text-align: center;
}

/* Container styles */
.container-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 100px;
    outline: 2px solid #243328;
    margin: 0;
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 50px; /* Adjust logo size */
    margin-right: 10px;
}

.logo-container b {
    font-family: 'Urbanist', sans-serif;
    font-size: 16px; /* Adjust the size as needed */
    font-weight: bold;
    color: #243328;
}

/* Navigation styles */
.nav-center {
    display: flex;
    gap: 30px;
    font-size: 14px;
}

.nav-button {
    font-family: 'Urbanist', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #243328;
    text-transform: uppercase;
    text-decoration: none;
    letter-spacing: 0.15em;
    padding: 15px 20px;
    position: relative;
    transition: background 0.3s;
}

.nav-button:hover {
    background: #f0f0f0; /* Add hover effect */
}

/* Ensure the link styles apply correctly */
a.nav-button {
    color: #243328;
}

/* Remove any unwanted styles from anchor tags */
a {
    text-decoration: none;
    color: inherit;
}

/* Login section styles */
.login-section {
    background-image: url('bg.jpg');
    background-size: cover; /* Use 'cover' to maintain aspect ratio and cover the entire container */
    background-repeat: no-repeat; /* Prevent the background from repeating */
    background-position: center; /* Center the background image */
    flex: 1;
    padding: 40px 20px; /* Adjust padding for responsiveness */
    text-align: center;
    margin-top: 0;/*40px*/
}

.section-title {
    font-size: 24px;
    color: #333;
    margin-top: 20px; /* Adjust the margin-top value as needed */
    margin-bottom: 20px;
}

h2.section-title {
    margin-top: 80px; /* Adjust the margin-top value as needed */
}

/* Form styles */
form {
    margin-top: 0; /*60px*/
    display: flex;
    flex-direction: column;
    align-items: center;
}

label {
    font-weight: bold;
    font-size: 12px;
    color: #333;
}

input {
    width: 100%; /* Adjust the width for responsiveness */
    max-width: 300px; /* Add max-width for responsiveness */
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 5px;
}

input[type="submit"] {
    width: 100%; /* Adjust the width for responsiveness */
    max-width: 300px; /* Add max-width for responsiveness */
    background-color: #35e85f;
    color: #fff;
    font-weight: bold;
    border: none;
    padding: 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

input[type="submit"]:hover {
    background-color: #45a045;
}

/* Footer styles */
.footer {
    background: #fff;
    padding: 10px 5px; /* Adjust padding for responsiveness 20 10*/
    text-align: center;
    margin: 0px;
}
.logo-container {
    display: flex;
    align-items: center;
}

.logo-container .h1 {
    display: flex;
    align-items: center;
    font-family: 'Urbanist', sans-serif;
}

.logo {
    max-width: 50px; /* Adjust the width as needed */
    height: auto;
    margin-right: 10px; /* Add some margin for spacing */
}

h1 {
    font-size: 18px; /* Adjust the font size as needed */
    margin: 0; /* Remove default margin */
    font-family: 'Urbanist', sans-serif;
}

.container-footer {
    font-size: 12px;
}

/* Media Query for Tablets and Smaller Screens */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
    }
    .nav-list {
        flex-direction: column;
        gap: 10px;
    }
    .login-section {
        padding: 40px 10px;
    }
    input[type="submit"] {
        width: 100%;
        max-width: none;
    }
}
/* Add this style for the error message */
.ermessage {
    color: rgb(255, 244, 244);
    margin-top: 10px; /* Adjust the margin-top value as needed */
    margin-left: 10px;
    font-style: italic;
    font-size: 14px;
}