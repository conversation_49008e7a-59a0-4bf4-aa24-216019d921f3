<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start(); // Start a session
include 'db_connection.php';

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Retrieve username and password from the login form
    $username = $_POST["username"];
    $password = $_POST["password"];

    $preRegisteredAccounts = [
        "Baao" => "b1top15",
        "<PERSON><PERSON><PERSON>" => "b2top15",
        "Bato" => "b3top15",
        "Bombon" => "b4top15",
        "Buhi" => "b5top15",
        "Bula" => "b6top15",
        "Cabusao" => "c1top15",
        "Calabanga" => "c2top15",
        "Camaligan" => "c3top15",
        "Canaman" => "c4top15",
        "Caramoan" => "c5top15",
        "Del Gallego" => "d1top15",
        "Gainza" => "g1top15",
        "<PERSON>ar<PERSON><PERSON>na" => "g2top15",
        "Goa" => "g3top15",
        "Lagonoy" => "l1top15",
        "Libmanan" => "l2top15",
        "Lu<PERSON>" => "l3top15",
        "Magarao" => "m1top15",
        "Milaor" => "m2top15",
        "Minalabac" => "m3top15",
        "Nabua" => "n1top15",
        "Ocampo" => "o1top15",
        "Pamplona" => "p1top15",
        "Pasacao" => "p2top15",
        "Parubcan" => "p3top15",
        "Pili" => "p4top15",
        "Ragay" => "r1top15",
        "Sagnay" => "s1top15",
        "San Fernando" => "s2top15",
        "San Jose" => "s3top15",
        "Sipocot" => "s4top15",
        "Siruma" => "s5top15",
        "Tigaon" => "t1top15",
        "Tinambac" => "t2top15",
    ];

    // Check if the entered credentials match a pre-registered account
    if (array_key_exists($username, $preRegisteredAccounts) && $preRegisteredAccounts[$username] === $password) {
        // Authentication successful

        // Check if the user is in the execom_members table
        $check_execom_sql = "SELECT COUNT(*) as count FROM execom_members WHERE candidate_name = '$username'";
        $check_execom_result = $conn->query($check_execom_sql);

        if ($check_execom_result) {
            $row = $check_execom_result->fetch_assoc();
            $count = $row['count'];

            if ($count > 0) {
                // User is a member of execom_members, redirect to the appropriate page
                header("Location: c1.php"); // Update with the correct page
                exit();
            } else {
                // User is not a member of execom_members, show an error message
                $errorMessage = "Incorrect username or password.";
            }
        } else {
            // Error checking execom membership
            $errorMessage = "Error checking execom membership: " . $conn->error;
        }
    } else {
        // Invalid credentials, show an error message
        $errorMessage = 'Incorrect username or password.';
    }
}

// Include the HTML template
include('bod_login.html');
?>
