body {
    font-family: 'Urbanist', sans-serif;
    background-color: #f0f0f0;
    text-align: center;
    margin: 0;
    padding: 0;
}

/* Center and responsive container */
.container-nav,  .container {
    width: 100%; 
    max-width: 1200px; 
    margin: 0 auto;
    padding: 0 10px; 
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
}

.header {
    background: #D3DCE5;
    padding: 25px 0;
}

/* Footer styling */
.footer {
    background: #fff;
    padding: 5px 0;
    text-align: center; 
}

.container-footer {
    display: flex; 
    justify-content: center; 
    align-items: center; 
    width: 100%; 
}

.footer-text {
    display: flex; 
    justify-content: center;
    align-items: right; 
    font-size: 12px;
    color: #2d4863;
    margin: 0; 
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 70px;
    display: block;
    height: auto;
    margin-right: 10px;
}

/* Link underline effect */
a {
    color: #243328;
    position: relative;
    padding: 5px 10px;
    text-transform: uppercase;
    transition: color 0.3s;
}

a:after {
    content: "";
    position: absolute;
    height: 2px;
    left: 50%;
    bottom: 0;
    width: 0;
    background: #243328;
    transition: width 0.3s ease, left 0.3s ease;
}

a:hover:after {
    width: 100%;
    left: 0;
}


/* Content styling */
.content {
    color: #2D4863;
    padding: 10px 0;
    width: 100%;
    display: flex;
    justify-content: center; 
    align-items: center; 
    flex: 1; 
    text-align: center; 
    flex-direction: column; 
}

/* General Links */
a {
    text-decoration: none;
    color: inherit;
}

form {
    background-color: white;
    padding: 10px;
    border-radius: 10px;
    margin: 40px auto; 
    max-width: 800px;
}

/* Form and Form Section */
.form-section {
    background-image: url('ff/admin.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 0;
    height: calc(100vh - 80px); 
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -73px;
}

.form-container {
    max-width: 100%; 
    max-height: 78vh; 
    margin: 1.5 auto; 
    background-color: #ffffff; 
    border-radius: 8px; 
    overflow-y: auto;
    position: relative;
    z-index: 1; 
}

.highlight {
    background-color: #ffffcc; 
    font-weight: bold; 
}

/* Admin Container */
.admin-container {
    position: relative; 
    width: auto; 
    height: auto; 
    display: flex;
    justify-content: center;
    align-items: center; 
}

.admin-container img {
    width: 100%; 
    height: auto; 
    display: block; 
    border-radius: 10px; 
    margin-top: -100px;
}

.top35 {
    position: absolute; 
    top: 50%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
    display: flex; 
    flex-direction: column; 
    align-items: center; 
    gap: 12px; 
}

.top35 h1 {
    color: #3D8B65;
    margin-top: -120px;
    font-weight: bolder;
}

.top35 a {
    font-size: 16px; 
    font-weight: bold;
    color: #FFFFFF;
    text-decoration: none;
    padding: 10px 0px; 
    background-color: #3D8A65;
    border-radius: 20px; 
    transition: background-color 0.3s; 
    padding: 15px 65px;
}

.top35 a:hover {
    background-color: #144b30;
    color: rgb(255, 255, 255); 
}

.link-container {
    display: flex;
    gap: 40px; 
    justify-content: left;
}

.table-results {
    width: 100%;
    margin: 0 auto; 
    background-color: #fff; 
    padding: 10px;
    border-radius: 8px; 
}

.candidates-wrapper {
    display: flex; /* Use flexbox for side-by-side layout */
    justify-content: space-between; /* Space between the forms */
    padding: 10px; /* Padding around the forms 10px*/
    gap: 20px; /* Add gap to space out the containers */
}

.candidate-form { /*first form*/
    flex: 1 1 32%; /* Change to 32% to give more width to each container */
    margin: 20px; /* Reduced margin for more space */
    background-color: #fff; 
    border-radius: 8px; 
    padding: 5px; 
}

table {
    width: 100%;
    border-collapse: collapse; 
}

th, td {
    padding: 4.5px; 
    text-align: left; 
    border-bottom: 1px solid #ddd; 
    font-size: 18px; 
}

th {
    background-color: #3D8B65; 
    color: white; 
    text-align: center; 
}
table th:last-child,
table td:last-child {
    text-align: center; 
}

.form-container .h2 {
    margin-bottom: 10px; 
    color: #3D8B65; 
    text-align: center;
    font-size: 24px;
    font-weight: bold; 
}
