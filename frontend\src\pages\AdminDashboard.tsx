import { ExportResults } from '@/components/admin/ExportResults'
import { RealTimeNotifications } from '@/components/admin/RealTimeNotifications'
import { ResultsDisplayTable } from '@/components/admin/ResultsDisplayTable'
import { StatisticsCards } from '@/components/admin/StatisticsCards'
import { UserActivityLogs } from '@/components/admin/UserActivityLogs'
import { UserForm } from '@/components/admin/UserForm'
import { UserManagementTable } from '@/components/admin/UserManagementTable'
import { VoteTrackingCards } from '@/components/admin/VoteTrackingCards'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  useAdminDashboard,
  useAdminResults,
  useBulkUserOperations,
  useCreateUser,
  useDeleteUser,
  useResetUserVote,
  useUpdateUser,
  useUsers,
} from '@/hooks/useAdmin'
import { useAuth } from '@/hooks/useAuth'
import { useDistrictsAndMunicipalities } from '@/hooks/useSystem'
import { debounce } from '@/lib/utils'
import { User } from '@/types'
import { LogOut, Plus, Search } from 'lucide-react'
import { useState } from 'react'

const AdminDashboard = () => {
  // State for filters and pagination
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: '',
    role: '',
    municipality: '',
    district: '',
    hasVoted: undefined as boolean | undefined,
    sort: 'createdAt',
    order: 'desc' as 'asc' | 'desc',
  })

  // State for user management
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)

  // State for admin results
  const [showCandidateNames, setShowCandidateNames] = useState(false)
  const [sortBy, setSortBy] = useState('votes')

  // API hooks
  const { data: dashboardData, isLoading: isDashboardLoading } = useAdminDashboard()
  const { data: usersData, isLoading: isUsersLoading } = useUsers(filters)
  const { data: adminResults, isLoading: isResultsLoading } = useAdminResults(
    showCandidateNames,
    sortBy
  )
  const createUserMutation = useCreateUser()
  const updateUserMutation = useUpdateUser()
  const deleteUserMutation = useDeleteUser()
  const resetVoteMutation = useResetUserVote()
  const bulkOperationMutation = useBulkUserOperations()

  // Auth hook for logout
  const { logout } = useAuth()

  // System data hooks
  const { data: systemData, isLoading: isSystemLoading } = useDistrictsAndMunicipalities()

  // Debounced search
  const debouncedSearch = debounce((searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }))
  }, 300)

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value)
  }

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value,
      page: 1,
    }))
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId) ? prev.filter(id => id !== userId) : [...prev, userId]
    )
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected && usersData?.data) {
      setSelectedUsers(usersData.data.users.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  const handleCreateUser = (userData: any) => {
    createUserMutation.mutate(userData, {
      onSuccess: () => {
        setIsUserDialogOpen(false)
      },
    })
  }

  const handleUpdateUser = (userData: any) => {
    if (editingUser) {
      updateUserMutation.mutate(
        { id: editingUser.id, userData },
        {
          onSuccess: () => {
            setIsUserDialogOpen(false)
            setEditingUser(null)
          },
        }
      )
    }
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setIsUserDialogOpen(true)
  }

  const handleDeleteUser = (userId: string) => {
    deleteUserMutation.mutate(userId)
  }

  const handleResetVote = (userId: string) => {
    resetVoteMutation.mutate(userId)
  }

  const handleViewUser = (user: User) => {
    // TODO: Implement user details view modal
    console.log('View user:', user)
  }

  const handleBulkOperation = (action: string) => {
    if (selectedUsers.length === 0) return

    bulkOperationMutation.mutate(
      { userIds: selectedUsers, action: action as any },
      {
        onSuccess: () => {
          setSelectedUsers([])
        },
      }
    )
  }

  const closeUserDialog = () => {
    setIsUserDialogOpen(false)
    setEditingUser(null)
  }

  // Handler for toggling candidate names display
  const handleToggleNames = (show: boolean) => {
    setShowCandidateNames(show)
  }

  // Handler for logout
  const handleLogout = () => {
    logout()
  }

  return (
    <div className='bg-background min-h-screen py-8'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        {/* Header */}
        <div className='mb-8 flex items-center justify-between'>
          <div>
            <h1 className='font-heading text-3xl font-bold'>Admin Dashboard</h1>
            <p className='text-muted-foreground mt-2'>
              Manage the voting system and view comprehensive results
            </p>
          </div>
          <div className='flex items-center gap-3'>
            <RealTimeNotifications />
            <Button variant='outline' onClick={handleLogout} className='flex items-center gap-2'>
              <LogOut className='h-4 w-4' />
              Logout
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className='mb-8'>
          <StatisticsCards data={dashboardData?.stats || null} isLoading={isDashboardLoading} />
        </div>

        {/* Vote Tracking Cards */}
        <div className='mb-8'>
          <h2 className='mb-4 text-2xl font-semibold'>Vote Tracking & Analytics</h2>
          <VoteTrackingCards
            stats={adminResults?.totalStats || null}
            isLoading={isResultsLoading}
          />
        </div>

        {/* Results Display */}
        <div className='mb-8'>
          <ResultsDisplayTable
            resultsByDistrict={adminResults?.resultsByDistrict || null}
            unifiedResults={adminResults?.unifiedResults || null}
            totalStats={adminResults?.totalStats || null}
            showCandidateNames={showCandidateNames}
            isLoading={isResultsLoading}
            onToggleNames={handleToggleNames}
          />
        </div>

        {/* Export and Activity Logs */}
        <div className='mb-8 grid gap-6 lg:grid-cols-2'>
          <ExportResults
            resultsByDistrict={adminResults?.resultsByDistrict || null}
            totalStats={adminResults?.totalStats || null}
          />
          <UserActivityLogs />
        </div>

        {/* User Management Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle>User Management</CardTitle>
              <div className='flex items-center space-x-2'>
                <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Add User
                    </Button>
                  </DialogTrigger>
                  <UserForm
                    user={editingUser}
                    onSubmit={editingUser ? handleUpdateUser : handleCreateUser}
                    onCancel={closeUserDialog}
                    isLoading={createUserMutation.isLoading || updateUserMutation.isLoading}
                  />
                </Dialog>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters and Search */}
            <div className='mb-6 flex flex-col gap-4 sm:flex-row'>
              <div className='flex-1'>
                <div className='relative'>
                  <Search className='text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform' />
                  <Input
                    placeholder='Search users...'
                    className='pl-10'
                    onChange={handleSearchChange}
                  />
                </div>
              </div>

              <Select
                value={filters.role || 'all'}
                onValueChange={(value: string) => handleFilterChange('role', value)}
              >
                <SelectTrigger className='w-[180px]'>
                  <SelectValue placeholder='Filter by role' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All roles</SelectItem>
                  <SelectItem value='voter'>Voter</SelectItem>
                  <SelectItem value='admin'>Admin</SelectItem>
                  <SelectItem value='execom'>Executive Committee</SelectItem>
                  <SelectItem value='tie-breaker'>Tie-breaker</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.hasVoted?.toString() || 'all'}
                onValueChange={(value: string) =>
                  handleFilterChange('hasVoted', value === 'all' ? undefined : value === 'true')
                }
              >
                <SelectTrigger className='w-[180px]'>
                  <SelectValue placeholder='Voting status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All users</SelectItem>
                  <SelectItem value='true'>Voted</SelectItem>
                  <SelectItem value='false'>Not voted</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.district || 'all'}
                onValueChange={(value: string) => handleFilterChange('district', value)}
              >
                <SelectTrigger className='w-[180px]'>
                  <SelectValue placeholder='Filter by district' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All districts</SelectItem>
                  {systemData?.districts.map(district => (
                    <SelectItem key={district} value={district}>
                      {district}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bulk Actions */}
            {selectedUsers.length > 0 && (
              <div className='bg-muted mb-4 flex items-center gap-2 rounded-lg p-3'>
                <span className='text-sm font-medium'>{selectedUsers.length} user(s) selected</span>
                <div className='ml-auto flex gap-2'>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant='outline' size='sm'>
                        Activate
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Activate Users</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to activate {selectedUsers.length} selected user(s)?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleBulkOperation('activate')}>
                          Activate
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant='outline' size='sm'>
                        Deactivate
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Deactivate Users</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to deactivate {selectedUsers.length} selected
                          user(s)?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleBulkOperation('deactivate')}>
                          Deactivate
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant='destructive' size='sm'>
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Users</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete {selectedUsers.length} selected user(s)?
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleBulkOperation('delete')}
                          className='bg-red-600 hover:bg-red-700'
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            )}

            {/* Users Table */}
            <UserManagementTable
              users={usersData?.data?.users || []}
              isLoading={isUsersLoading}
              onEditUser={handleEditUser}
              onDeleteUser={handleDeleteUser}
              onResetVote={handleResetVote}
              onViewUser={handleViewUser}
              selectedUsers={selectedUsers}
              onSelectUser={handleSelectUser}
              onSelectAll={handleSelectAll}
            />

            {/* Pagination */}
            {usersData?.data?.pagination && (
              <div className='mt-6 flex items-center justify-between'>
                <div className='text-muted-foreground text-sm'>
                  Showing{' '}
                  {(usersData.data.pagination.currentPage - 1) * usersData.data.pagination.limit +
                    1}{' '}
                  to{' '}
                  {Math.min(
                    usersData.data.pagination.currentPage * usersData.data.pagination.limit,
                    usersData.data.pagination.totalUsers
                  )}{' '}
                  of {usersData.data.pagination.totalUsers} users
                </div>
                <div className='flex items-center space-x-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(usersData.data.pagination.currentPage - 1)}
                    disabled={!usersData.data.pagination.hasPrevPage}
                  >
                    Previous
                  </Button>
                  <span className='text-sm'>
                    Page {usersData.data.pagination.currentPage} of{' '}
                    {usersData.data.pagination.totalPages}
                  </span>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(usersData.data.pagination.currentPage + 1)}
                    disabled={!usersData.data.pagination.hasNextPage}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default AdminDashboard
