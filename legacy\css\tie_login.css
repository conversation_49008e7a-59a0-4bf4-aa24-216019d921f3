/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
}

/* Global styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f7f7f7;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header styles */
.header {
    background: #243328;
    color: #fff;
    padding: 40px 0;
    text-align: center;
}

.header-title {
    font-size: 36px;
    margin-bottom: 20px;
}

/* Navigation bar styles */
.navigation {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
    text-align: center;
}

.nav-list {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.nav-item {
    font-size: 18px;
    font-weight: bold;
}

.nav-link {
    color: #243328;
}

/* Login section styles */
.login-section {
    flex: 1;
    padding: 40px 0;
    text-align: center;
    background-color: #f0f0f0;
}

.section-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

/* Form styles */form {
    display: flex;
    flex-direction: column;
    align-items: center;
}

label {
    font-weight: bold;
    color: #333;
}

/* Narrow the input boxes */
input {
    width: 300px; /* Adjust the width as needed */
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 5px;
}

input[type="submit"] {
    background-color: #243328;
    color: #fff;
    font-weight: bold;
    border: none;
    padding: 15px 30px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

input[type="submit"]:hover {
    background-color: #45a045;
}

/* Footer styles */
.footer {
    background: #fff;
    padding: 20px 0;
    text-align: center;
    color: #333;
}
