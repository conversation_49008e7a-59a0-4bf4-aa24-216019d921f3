<?php
include 'db_connection.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Fetch candidate information from execom_members table
$sql = "SELECT candidate_id, candidate_name FROM execom_members";
$result = $conn->query($sql);

$candidates = [];
while ($row = $result->fetch_assoc()) {
    $candidates[] = $row;
}

// Sort candidates alphabetically by candidate_name
usort($candidates, function($a, $b) {
    return strcasecmp($a['candidate_name'], $b['candidate_name']);
});

$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Vote</title>
    <link rel="stylesheet" type="text/css" href="vote.css">
    <script>
        function validateForm() {
            var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            if (checkboxes.length > 15) {
                alert('You can vote for up to 15 candidates only.');
                return false;
            }
            return true;
        }

        function limitCheckboxes() {
            var checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].addEventListener('change', function() {
                    var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                    if (checkedCheckboxes.length > 1) {
                        alert('You can vote 1 candidate only.');
                        this.checked = false;
                    }
                });
            }
        }

        window.onload = limitCheckboxes;
    </script>
</head>
<body>
    <h1>Cast Your Vote</h1>
    <form action="vote_chairperson.php" method="post" onsubmit="return validateForm()">
        <label>Choose Your Candidate:</label><br>
        <div class="candidates">
            <?php foreach ($candidates as $candidate) : ?>
                <div><input type="checkbox" name="candidate_id[]" value="<?php echo $candidate['candidate_id']; ?>"> <?php echo $candidate['candidate_name']; ?></div>
            <?php endforeach; ?>
        </div>
        <input type="submit" value="Vote">
    </form>
</body>
</html>
