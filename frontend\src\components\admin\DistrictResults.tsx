import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { formatNumber, formatPercentage } from '@/lib/utils'
import { District } from '@/types'
import { Eye, EyeOff, MapPin, TrendingUp, Users } from 'lucide-react'
import { useState } from 'react'

interface DistrictStats {
  district: District
  totalVoters: number
  votedCount: number
  pendingVoters: number
  participationRate: number
  municipalities: string[]
  municipalityCount: number
}

interface DistrictResultsProps {
  districtStats: DistrictStats[]
  isLoading: boolean
  resultsVisible: boolean
  votingActive: boolean
  onToggleResults: (visible: boolean) => void
  onSelectDistrict: (district: District | null) => void
  selectedDistrict: District | null
}

export function DistrictResults({
  districtStats,
  isLoading,
  resultsVisible,
  votingActive,
  onToggleResults,
  onSelectDistrict,
  selectedDistrict,
}: DistrictResultsProps) {
  const [showDetails, setShowDetails] = useState(false)

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-10 w-32' />
        </div>
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className='h-48' />
          ))}
        </div>
      </div>
    )
  }

  const totalVoters = districtStats.reduce((sum, district) => sum + district.totalVoters, 0)
  const totalVoted = districtStats.reduce((sum, district) => sum + district.votedCount, 0)
  const overallParticipation = totalVoters > 0 ? (totalVoted / totalVoters) * 100 : 0

  return (
    <div className='space-y-6'>
      {/* Header with controls */}
      <div className='flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center'>
        <div>
          <h2 className='text-2xl font-bold'>District-Based Results</h2>
          <p className='text-muted-foreground'>
            Voting progress and results by congressional district
          </p>
        </div>

        <div className='flex items-center gap-3'>
          {/* District filter */}
          <Select
            value={selectedDistrict || ''}
            onValueChange={value => onSelectDistrict((value as District) || null)}
          >
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='All districts' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=''>All districts</SelectItem>
              {DISTRICTS.map(district => (
                <SelectItem key={district} value={district}>
                  {district}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Results visibility toggle */}
          <Button
            variant={resultsVisible ? 'default' : 'outline'}
            onClick={() => onToggleResults(!resultsVisible)}
            className='flex items-center gap-2'
          >
            {resultsVisible ? (
              <>
                <Eye className='h-4 w-4' />
                Results Visible
              </>
            ) : (
              <>
                <EyeOff className='h-4 w-4' />
                Results Hidden
              </>
            )}
          </Button>

          {/* Details toggle */}
          <Button variant='outline' onClick={() => setShowDetails(!showDetails)}>
            {showDetails ? 'Hide' : 'Show'} Details
          </Button>
        </div>
      </div>

      {/* Overall statistics */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <TrendingUp className='h-5 w-5' />
            Overall Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid gap-4 md:grid-cols-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>{formatNumber(totalVoters)}</div>
              <div className='text-muted-foreground text-sm'>Total Voters</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>{formatNumber(totalVoted)}</div>
              <div className='text-muted-foreground text-sm'>Votes Cast</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-orange-600'>
                {formatNumber(totalVoters - totalVoted)}
              </div>
              <div className='text-muted-foreground text-sm'>Pending</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {formatPercentage(overallParticipation)}
              </div>
              <div className='text-muted-foreground text-sm'>Participation</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* District cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {districtStats
          .filter(district => !selectedDistrict || district.district === selectedDistrict)
          .map(district => (
            <Card
              key={district.district}
              className={`transition-all hover:shadow-md ${
                selectedDistrict === district.district ? 'ring-primary ring-2' : ''
              }`}
            >
              <CardHeader className='pb-3'>
                <div className='flex items-center justify-between'>
                  <CardTitle className='flex items-center gap-2 text-lg'>
                    <MapPin className='h-4 w-4' />
                    {district.district}
                  </CardTitle>
                  <Badge
                    variant={
                      district.participationRate >= 80
                        ? 'success'
                        : district.participationRate >= 50
                          ? 'warning'
                          : 'secondary'
                    }
                  >
                    {formatPercentage(district.participationRate)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Voting statistics */}
                <div className='grid grid-cols-3 gap-3 text-center'>
                  <div>
                    <div className='text-lg font-semibold text-blue-600'>
                      {formatNumber(district.totalVoters)}
                    </div>
                    <div className='text-muted-foreground text-xs'>Total</div>
                  </div>
                  <div>
                    <div className='text-lg font-semibold text-green-600'>
                      {formatNumber(district.votedCount)}
                    </div>
                    <div className='text-muted-foreground text-xs'>Voted</div>
                  </div>
                  <div>
                    <div className='text-lg font-semibold text-orange-600'>
                      {formatNumber(district.pendingVoters)}
                    </div>
                    <div className='text-muted-foreground text-xs'>Pending</div>
                  </div>
                </div>

                {/* Progress bar */}
                <div className='space-y-2'>
                  <div className='flex justify-between text-sm'>
                    <span>Progress</span>
                    <span>{formatPercentage(district.participationRate)}</span>
                  </div>
                  <div className='h-2 w-full rounded-full bg-gray-200'>
                    <div
                      className='bg-primary h-2 rounded-full transition-all duration-300'
                      style={{ width: `${district.participationRate}%` }}
                    />
                  </div>
                </div>

                {/* Municipality count */}
                <div className='flex items-center justify-between text-sm'>
                  <span className='flex items-center gap-1'>
                    <Users className='h-4 w-4' />
                    Municipalities
                  </span>
                  <span className='font-medium'>{district.municipalityCount}</span>
                </div>

                {/* Municipality details */}
                {showDetails && (
                  <div className='border-border border-t pt-3'>
                    <div className='mb-2 text-sm font-medium'>Municipalities:</div>
                    <div className='flex flex-wrap gap-1'>
                      {DISTRICT_MAPPING[district.district].map(municipality => (
                        <Badge key={municipality} variant='outline' className='text-xs'>
                          {municipality}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action button */}
                <Button
                  variant='outline'
                  size='sm'
                  className='w-full'
                  onClick={() =>
                    onSelectDistrict(
                      selectedDistrict === district.district ? null : district.district
                    )
                  }
                >
                  {selectedDistrict === district.district ? 'Show All' : 'View Details'}
                </Button>
              </CardContent>
            </Card>
          ))}
      </div>

      {/* Results visibility warning */}
      {votingActive && resultsVisible && (
        <Card className='border-orange-200 bg-orange-50'>
          <CardContent className='py-4'>
            <div className='flex items-center gap-3'>
              <Eye className='h-5 w-5 text-orange-600' />
              <div className='text-sm text-orange-900'>
                <strong>Results are currently visible to voters.</strong> Consider hiding results
                during active voting to prevent influence on voting decisions.
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No results message */}
      {!votingActive && !resultsVisible && (
        <Card className='border-blue-200 bg-blue-50'>
          <CardContent className='py-4'>
            <div className='flex items-center gap-3'>
              <EyeOff className='h-5 w-5 text-blue-600' />
              <div className='text-sm text-blue-900'>
                Results are hidden from public view. Use the toggle above to make results visible
                when voting is complete.
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
