<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Voting System - Login</title>
    <link rel="stylesheet" type="text/css" href="tie_login.css"> <!-- Link to your CSS file -->
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="header-title">In Case of Tie Election</h1>
        </div>
    </header>

    <nav class="navigation">
        <ul class="nav-list">
            <li class="nav-item"><a class="nav-link" href="tie_loginform.php">Tie Login</a></li>
            <li class="nav-item"><a class="nav-link" href="admin_login.html">Admin Login</a></li> <!-- Added a link for admin login -->
        </ul>
    </nav>

    <section class="login-section">
        <div class="container">
            <h2 class="section-title">Voting for Tie Candidates</h2>
            
            <?php
                // Check if the error message is set
                if (isset($errorMessage)) {
                    echo '<p class="error-message">' . '</p>';
                }
            ?>
            
            <form action="tie_login.php" method="post">
                <label for="username">Username:</label>
                <input type="text" name="username" required><br>
    
                <label for="password">Password:</label>
                <input type="password" name="password" required><br>
    
                <input type="submit" value="Login">
            </form>
        </div>
    </section>
    

    <footer class="footer">
        <div class="container">
            &copy; SOCMOB
        </div>
    </footer>
</body>
</html>
