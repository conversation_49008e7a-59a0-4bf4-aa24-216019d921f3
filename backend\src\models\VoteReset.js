import mongoose from 'mongoose'

const voteResetSchema = new mongoose.Schema(
  {
    // Admin who performed the reset
    adminId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Admin ID is required'],
      index: true,
    },
    adminUsername: {
      type: String,
      required: [true, 'Admin username is required'],
    },
    
    // Reset details
    resetType: {
      type: String,
      enum: {
        values: ['all-votes', 'user-votes', 'candidate-votes'],
        message: 'Invalid reset type',
      },
      required: [true, 'Reset type is required'],
    },
    
    // Affected data
    affectedUsersCount: {
      type: Number,
      required: [true, 'Affected users count is required'],
      min: [0, 'Affected users count cannot be negative'],
    },
    affectedVotesCount: {
      type: Number,
      required: [true, 'Affected votes count is required'],
      min: [0, 'Affected votes count cannot be negative'],
    },
    
    // Optional: specific user or candidate affected
    targetUserId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      index: true,
    },
    targetCandidateId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Candidate',
      index: true,
    },
    
    // Reason and metadata
    reason: {
      type: String,
      maxlength: [500, 'Reason cannot exceed 500 characters'],
    },
    
    // System metadata
    ipAddress: {
      type: String,
      required: [true, 'IP address is required'],
    },
    userAgent: {
      type: String,
      required: [true, 'User agent is required'],
    },
    
    // Backup reference (if backup was created)
    backupId: {
      type: String,
      index: true,
    },
    
    // Status tracking
    status: {
      type: String,
      enum: {
        values: ['pending', 'completed', 'failed', 'rolled-back'],
        message: 'Invalid status',
      },
      default: 'pending',
    },
    
    // Error information (if failed)
    errorMessage: {
      type: String,
      maxlength: [1000, 'Error message cannot exceed 1000 characters'],
    },
    
    // Completion timestamp
    completedAt: {
      type: Date,
    },
    
    // Statistics before reset
    preResetStats: {
      totalVotes: {
        type: Number,
        default: 0,
      },
      totalVoters: {
        type: Number,
        default: 0,
      },
      votedUsersCount: {
        type: Number,
        default: 0,
      },
    },
    
    // Statistics after reset
    postResetStats: {
      totalVotes: {
        type: Number,
        default: 0,
      },
      totalVoters: {
        type: Number,
        default: 0,
      },
      votedUsersCount: {
        type: Number,
        default: 0,
      },
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        delete ret.ipAddress
        delete ret.userAgent
        return ret
      },
    },
  }
)

// Indexes for performance
voteResetSchema.index({ adminId: 1, createdAt: -1 })
voteResetSchema.index({ resetType: 1, createdAt: -1 })
voteResetSchema.index({ status: 1, createdAt: -1 })
voteResetSchema.index({ createdAt: -1 })

// Virtual for duration calculation
voteResetSchema.virtual('duration').get(function () {
  if (this.completedAt && this.createdAt) {
    return this.completedAt.getTime() - this.createdAt.getTime()
  }
  return null
})

// Static method to get reset statistics
voteResetSchema.statics.getResetStatistics = function (startDate, endDate) {
  const matchConditions = {}
  
  if (startDate || endDate) {
    matchConditions.createdAt = {}
    if (startDate) matchConditions.createdAt.$gte = new Date(startDate)
    if (endDate) matchConditions.createdAt.$lte = new Date(endDate)
  }

  return this.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: null,
        totalResets: { $sum: 1 },
        totalVotesReset: { $sum: '$affectedVotesCount' },
        totalUsersAffected: { $sum: '$affectedUsersCount' },
        resetsByType: {
          $push: {
            type: '$resetType',
            count: 1,
            votes: '$affectedVotesCount',
          },
        },
        resetsByStatus: {
          $push: {
            status: '$status',
            count: 1,
          },
        },
        adminActivity: {
          $push: {
            adminId: '$adminId',
            adminUsername: '$adminUsername',
            count: 1,
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        totalResets: 1,
        totalVotesReset: 1,
        totalUsersAffected: 1,
        resetsByType: 1,
        resetsByStatus: 1,
        adminActivity: 1,
      },
    },
  ])
}

// Static method to get recent resets
voteResetSchema.statics.getRecentResets = function (limit = 10) {
  return this.find()
    .populate('adminId', 'username municipality')
    .populate('targetUserId', 'username municipality')
    .populate('targetCandidateId', 'municipalityName district')
    .sort({ createdAt: -1 })
    .limit(limit)
}

// Instance method to mark as completed
voteResetSchema.methods.markCompleted = function (postResetStats) {
  this.status = 'completed'
  this.completedAt = new Date()
  if (postResetStats) {
    this.postResetStats = postResetStats
  }
  return this.save()
}

// Instance method to mark as failed
voteResetSchema.methods.markFailed = function (errorMessage) {
  this.status = 'failed'
  this.errorMessage = errorMessage
  this.completedAt = new Date()
  return this.save()
}

// Pre-save middleware for validation
voteResetSchema.pre('save', function (next) {
  // Ensure completed resets have completion timestamp
  if (this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date()
  }
  
  // Ensure failed resets have error message
  if (this.status === 'failed' && !this.errorMessage) {
    this.errorMessage = 'Reset failed without specific error message'
  }
  
  next()
})

const VoteReset = mongoose.model('VoteReset', voteResetSchema)

export default VoteReset
