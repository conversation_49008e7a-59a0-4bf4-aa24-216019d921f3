# Development Guide

## Available Scripts

### Root Level Scripts

From the project root directory, you can run:

```bash
# Quick setup (install all dependencies and create env files)
npm run setup

# Install dependencies for all projects
npm run install:all

# Start both backend and frontend development servers
npm run dev

# Build the frontend for production
npm run build

# Start the production server (backend only)
npm run start

# Run tests for both backend and frontend
npm run test

# Lint all code
npm run lint

# Fix linting issues
npm run lint:fix

# Format all code
npm run format

# Clean all node_modules and build directories
npm run clean

# Create environment files from examples
npm run setup:env
```

### Backend Scripts

From the `backend` directory:

```bash
# Development server with hot reload
npm run dev

# Production server
npm start

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Run database migration
npm run migrate

# Seed database with initial data
npm run seed
```

### Frontend Scripts

From the `frontend` directory:

```bash
# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run tests
npm test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Type checking
npm run type-check

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

## Development Workflow

### 1. Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd dfpta

# Quick setup (installs dependencies and creates env files)
npm run setup

# Edit environment files
# backend/.env - Configure database, JWT secrets, etc.
# frontend/.env - Configure API URL if different from default
```

### 2. Daily Development

```bash
# Start both servers (recommended)
npm run dev

# Or start individually:
# Terminal 1 - Backend
cd backend && npm run dev

# Terminal 2 - Frontend  
cd frontend && npm run dev
```

### 3. Code Quality

```bash
# Before committing, run:
npm run lint:fix  # Fix linting issues
npm run format    # Format code
npm run test      # Run all tests
```

## Project Structure

```
dfpta/
├── backend/                 # Node.js/Express API
│   ├── src/
│   │   ├── controllers/     # Route handlers
│   │   ├── models/         # MongoDB models
│   │   ├── routes/         # API routes
│   │   ├── middleware/     # Custom middleware
│   │   ├── utils/          # Utility functions
│   │   ├── config/         # Configuration files
│   │   └── app.js          # Express app setup
│   ├── tests/              # Backend tests
│   └── package.json
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services
│   │   ├── utils/          # Utility functions
│   │   ├── types/          # TypeScript types
│   │   └── App.tsx
│   └── package.json
├── docs/                   # Documentation
├── scripts/                # Utility scripts
└── package.json           # Root package.json
```

## Development Guidelines

### Code Style

- **Backend**: ESLint + Prettier with Node.js best practices
- **Frontend**: ESLint + Prettier with React/TypeScript rules
- **Formatting**: Prettier with consistent configuration
- **Commits**: Use conventional commit messages

### API Development

1. **Routes**: Define in `backend/src/routes/`
2. **Controllers**: Implement logic in `backend/src/controllers/`
3. **Models**: Define schemas in `backend/src/models/`
4. **Middleware**: Add custom middleware in `backend/src/middleware/`
5. **Documentation**: Use JSDoc comments for Swagger

### Frontend Development

1. **Components**: Create reusable components in `frontend/src/components/`
2. **Pages**: Page-level components in `frontend/src/pages/`
3. **Hooks**: Custom hooks in `frontend/src/hooks/`
4. **Services**: API calls in `frontend/src/services/`
5. **Types**: TypeScript definitions in `frontend/src/types/`

### Testing

- **Backend**: Jest + Supertest for API testing
- **Frontend**: Vitest + Testing Library for component testing
- **Coverage**: Aim for >80% test coverage
- **E2E**: Consider adding Playwright for end-to-end testing

## Environment Configuration

### Development

- Backend runs on `http://localhost:5000`
- Frontend runs on `http://localhost:3000`
- MongoDB on `mongodb://localhost:27017/dfpta_voting`
- API docs at `http://localhost:5000/api/docs`

### Environment Variables

#### Backend (.env)
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/dfpta_voting
JWT_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=DFPTA E-Voting System
```

## Debugging

### Backend Debugging

1. **Logs**: Check console output and log files
2. **Database**: Use MongoDB Compass or mongosh
3. **API**: Test endpoints with Postman or curl
4. **Debugging**: Use Node.js debugger or VS Code debugging

### Frontend Debugging

1. **Browser DevTools**: Use React Developer Tools
2. **Network**: Check API calls in Network tab
3. **State**: Use React Query DevTools
4. **Console**: Check for JavaScript errors

## Common Issues

### Port Conflicts
```bash
# Change backend port
# Edit backend/.env: PORT=5001
# Edit frontend/.env: VITE_API_URL=http://localhost:5001/api
```

### Database Connection
```bash
# Ensure MongoDB is running
mongosh  # Should connect successfully

# Check connection string in backend/.env
MONGODB_URI=mongodb://localhost:27017/dfpta_voting
```

### CORS Issues
```bash
# Ensure FRONTEND_URL is set correctly in backend/.env
FRONTEND_URL=http://localhost:3000
```

## Performance Tips

1. **Backend**: Use MongoDB indexes, implement caching
2. **Frontend**: Use React.memo, lazy loading, code splitting
3. **Build**: Optimize bundle size, use compression
4. **Database**: Monitor query performance, use aggregation pipelines

## Security Considerations

1. **Environment Variables**: Never commit .env files
2. **JWT Secrets**: Use strong, unique secrets
3. **Input Validation**: Validate all user inputs
4. **Rate Limiting**: Implement API rate limiting
5. **HTTPS**: Use HTTPS in production
