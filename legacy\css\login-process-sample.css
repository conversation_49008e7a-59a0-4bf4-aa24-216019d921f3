/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body {
    font-family: Tungsten-Bold, Arial, sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container-nav, .container-footer, .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header {
    background: #D3DCE5;
    padding: 25px 0;
}

.logo-container {
    display: flex;
    align-items: center;
}

.phone-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-center {
    display: flex;
    gap: 30px;
}

.logo {
    max-width: 70px;
    height: auto;
    display: block;
    margin-right: 20px;
}


.logo-text b {
    display: flex;
    align-items: center;
    font-weight: normal;
    font-family: 'Urbanist', sans-serif;
    margin-left: 20px;
}

.content {
    background-image: url('ff/bgg1.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    color: #2D4863;
    text-align: center;
    padding: 190px; /* Adjust the padding as needed */
    width: 100%;
    display: flex;
    position: relative;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0;
    box-sizing: border-box; /* Add this line */
}


.content-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin-top: -100px;
}

.content-text {
    text-align: center;
}


.section-title {
    font-size: 75px; 
    margin-bottom: 20px; 
    position: relative; 
    top: -70px; 
}

.section-description {
    text-align: center;
    font-size: 24px; 
    margin-top: -55px; 

}

.login-center {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
.login-container h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
  }

label {
    display: block;
    color: #666;
    margin-bottom: 5px;
  }

input[type="text"],
  input[type="password"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    border: 1px solid #ddd;
  }

.login-button {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: none;
    background-color: #4CAF50;
    color: white;
    cursor: pointer;
  }

.login-button:hover {
    background-color: #E98E53;
}

.footer {
    background: #fff;
    text-align: center;
    padding: 5px 0;
}

.footer-text {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
}

body, html {
    margin: 0;
    font: bold 14px/1.4 'Arial', arial, sans-serif;
    background: #000;
}

a {
    color: #243328;
    text-transform: uppercase;
    text-decoration: none;
    letter-spacing: 0.15em;
    display: inline-block;
    padding: 15px 20px;
    position: relative;
}

a:after {    
    background: none repeat scroll 0 0 transparent;
    bottom: 0;
    content: "";
    display: block;
    height: 2px;
    left: 50%;
    position: absolute;
    background: #243328;
    transition: width 0.3s ease 0s, left 0.3s ease 0s;
    width: 0;
}

a:hover:after { 
    width: 100%; 
    left: 0; 
}

/* Media queries for responsiveness */
@media screen and (max-width: 768px) {
    .container-nav, .container-footer, .container {
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-center {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .content-container {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 48px;
    }

    .section-description {
        font-size: 18px;
    }

    .login-button {
        padding: 10px 20px;
    }
}

@media screen and (max-width: 480px) {
    .section-title {
        font-size: 36px;
    }

    .section-description {
        font-size: 16px;
    }

    .login-button {
        padding: 8px 16px;
    }
}

@media screen and (max-height: 300px) {
    ul {
        margin-top: 40px;
    }
}

  
.login-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 0px 10px rgba(0,0,0,0.1);
    width: 300px;
  }
  
  .login-container h2 {
    text-align: center;
    color: #333;
  }
  
  .input-group {
    margin-bottom: 20px;
  }
  
  .input-group label {
    display: block;
    margin-bottom: 5px;
  }
  
  .input-group input {
    width: calc(100% - 20px);
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: #D3DCE5;
  }
  
  button {
    width: 100%;
    padding: 10px;
    background-color: #26704C;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  button:hover {
    background-color: #45a049;
  }
  