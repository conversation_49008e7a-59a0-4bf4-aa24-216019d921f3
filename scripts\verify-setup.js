#!/usr/bin/env node

import { existsSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🔍 Verifying DFPTA E-Voting System Setup...\n');

const checks = [
  {
    name: 'Project Structure',
    items: [
      { path: 'backend/package.json', desc: 'Backend package.json' },
      { path: 'frontend/package.json', desc: 'Frontend package.json' },
      { path: 'backend/src/app.js', desc: 'Backend main app' },
      { path: 'frontend/src/App.tsx', desc: 'Frontend main app' },
      { path: 'docs/setup-guide.md', desc: 'Setup documentation' },
      { path: 'legacy/README.md', desc: 'Legacy files documentation' },
    ]
  },
  {
    name: 'Backend Structure',
    items: [
      { path: 'backend/src/controllers', desc: 'Controllers directory' },
      { path: 'backend/src/models', desc: 'Models directory' },
      { path: 'backend/src/routes', desc: 'Routes directory' },
      { path: 'backend/src/middleware', desc: 'Middleware directory' },
      { path: 'backend/src/utils', desc: 'Utils directory' },
      { path: 'backend/src/config', desc: 'Config directory' },
    ]
  },
  {
    name: 'Frontend Structure',
    items: [
      { path: 'frontend/src/components', desc: 'Components directory' },
      { path: 'frontend/src/pages', desc: 'Pages directory' },
      { path: 'frontend/src/hooks', desc: 'Hooks directory' },
      { path: 'frontend/src/services', desc: 'Services directory' },
      { path: 'frontend/src/types', desc: 'Types directory' },
      { path: 'frontend/src/assets', desc: 'Assets directory' },
    ]
  },
  {
    name: 'Configuration Files',
    items: [
      { path: 'backend/.env.example', desc: 'Backend environment template' },
      { path: 'frontend/.env.example', desc: 'Frontend environment template' },
      { path: 'backend/.eslintrc.js', desc: 'Backend ESLint config' },
      { path: 'frontend/.eslintrc.cjs', desc: 'Frontend ESLint config' },
      { path: '.gitignore', desc: 'Git ignore file' },
      { path: '.vscode/settings.json', desc: 'VS Code settings' },
    ]
  },
  {
    name: 'Legacy Organization',
    items: [
      { path: 'legacy/php', desc: 'Legacy PHP files' },
      { path: 'legacy/html', desc: 'Legacy HTML files' },
      { path: 'legacy/css', desc: 'Legacy CSS files' },
      { path: 'legacy/assets', desc: 'Legacy assets' },
    ]
  }
];

let allPassed = true;

checks.forEach(check => {
  console.log(`📋 ${check.name}:`);
  
  check.items.forEach(item => {
    const fullPath = join(projectRoot, item.path);
    const exists = existsSync(fullPath);
    const status = exists ? '✅' : '❌';
    
    console.log(`  ${status} ${item.desc}`);
    
    if (!exists) {
      allPassed = false;
    }
  });
  
  console.log('');
});

// Check for clean root directory
console.log('📁 Root Directory Cleanliness:');
const rootItems = [
  'README.md',
  'package.json',
  'backend',
  'frontend',
  'docs',
  'scripts',
  'legacy',
  '.gitignore',
  '.vscode'
];

const allowedInRoot = new Set(rootItems);
const actualItems = [];

try {
  const fs = await import('fs');
  const items = fs.readdirSync(projectRoot);
  
  items.forEach(item => {
    if (!item.startsWith('.') || item === '.gitignore' || item === '.vscode') {
      actualItems.push(item);
    }
  });
  
  const extraItems = actualItems.filter(item => !allowedInRoot.has(item));
  
  if (extraItems.length === 0) {
    console.log('  ✅ Root directory is clean');
  } else {
    console.log('  ❌ Extra items in root directory:');
    extraItems.forEach(item => {
      console.log(`    - ${item}`);
    });
    allPassed = false;
  }
} catch (error) {
  console.log('  ❌ Could not check root directory');
  allPassed = false;
}

console.log('');

// Summary
if (allPassed) {
  console.log('🎉 All checks passed! Your DFPTA E-Voting System is properly set up.');
  console.log('');
  console.log('Next steps:');
  console.log('1. Run `npm run setup` to install dependencies');
  console.log('2. Configure environment variables in .env files');
  console.log('3. Start development with `npm run dev`');
} else {
  console.log('⚠️  Some checks failed. Please review the setup.');
  process.exit(1);
}

console.log('');
console.log('📚 Documentation:');
console.log('- Setup Guide: docs/setup-guide.md');
console.log('- Development Guide: docs/development-guide.md');
console.log('- Legacy Files: legacy/README.md');
