import mongoose from 'mongoose'

const tieBreakerSchema = new mongoose.Schema(
  {
    round: {
      type: Number,
      required: [true, 'Round number is required'],
      min: [1, 'Round must be at least 1'],
      index: true,
    },
    tieBreakerRound: {
      type: Number,
      required: [true, 'Tie-breaker round number is required'],
      min: [1, 'Tie-breaker round must be at least 1'],
      index: true,
    },
    tiedCandidates: [{
      candidateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Candidate',
        required: true,
      },
      originalVotes: {
        type: Number,
        required: true,
        min: 0,
      },
      tieBreakerVotes: {
        type: Number,
        default: 0,
        min: 0,
      },
      isWinner: {
        type: Boolean,
        default: false,
      },
      isEliminated: {
        type: Boolean,
        default: false,
      },
    }],
    status: {
      type: String,
      enum: {
        values: ['pending', 'active', 'completed', 'cancelled'],
        message: 'Invalid tie-breaker status'
      },
      default: 'pending',
      index: true,
    },
    tiePosition: {
      type: Number,
      required: [true, 'Tie position is required'],
      min: [1, 'Tie position must be at least 1'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    // Winner information
    winnerCandidateId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Candidate',
      index: true,
    },
    winnerVotes: {
      type: Number,
      min: 0,
    },
    // Voting session reference
    sessionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'VotingSession',
      required: [true, 'Session ID is required'],
      index: true,
    },
    // Timing information
    startedAt: {
      type: Date,
      index: true,
    },
    completedAt: {
      type: Date,
      index: true,
    },
    deadline: {
      type: Date,
      index: true,
    },
    // Voting statistics
    totalVotesRequired: {
      type: Number,
      min: 1,
    },
    totalVotesCast: {
      type: Number,
      default: 0,
      min: 0,
    },
    participatingVoters: [{
      voterId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      municipality: {
        type: String,
        required: true,
      },
      votedAt: {
        type: Date,
        default: Date.now,
      },
    }],
    // Resolution details
    resolutionMethod: {
      type: String,
      enum: ['voting', 'random', 'manual', 'criteria'],
      default: 'voting',
    },
    resolutionNotes: {
      type: String,
      trim: true,
      maxlength: [1000, 'Resolution notes cannot exceed 1000 characters'],
    },
    // Admin actions
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Creator ID is required'],
    },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    // Subsequent tie-breaker reference
    nextTieBreakerRound: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'TieBreaker',
    },
    previousTieBreakerRound: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'TieBreaker',
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Compound indexes
tieBreakerSchema.index({ round: 1, tieBreakerRound: 1 }, { unique: true })
tieBreakerSchema.index({ sessionId: 1, status: 1 })
tieBreakerSchema.index({ status: 1, startedAt: 1 })
tieBreakerSchema.index({ tiePosition: 1, round: 1 })

// Virtual for tie-breaker identifier
tieBreakerSchema.virtual('identifier').get(function() {
  return `R${this.round}-TB${this.tieBreakerRound}`
})

// Virtual for progress percentage
tieBreakerSchema.virtual('progressPercentage').get(function() {
  if (this.totalVotesRequired === 0) return 0
  return Math.round((this.totalVotesCast / this.totalVotesRequired) * 100)
})

// Virtual for remaining time
tieBreakerSchema.virtual('timeRemaining').get(function() {
  if (!this.deadline) return null
  const now = new Date()
  const remaining = this.deadline.getTime() - now.getTime()
  return remaining > 0 ? remaining : 0
})

// Instance method to start tie-breaker
tieBreakerSchema.methods.start = function() {
  this.status = 'active'
  this.startedAt = new Date()
  return this.save()
}

// Instance method to complete tie-breaker
tieBreakerSchema.methods.complete = function(winnerCandidateId, winnerVotes) {
  this.status = 'completed'
  this.completedAt = new Date()
  this.winnerCandidateId = winnerCandidateId
  this.winnerVotes = winnerVotes
  
  // Mark winner in tiedCandidates array
  const winner = this.tiedCandidates.find(c => c.candidateId.toString() === winnerCandidateId.toString())
  if (winner) {
    winner.isWinner = true
    winner.tieBreakerVotes = winnerVotes
  }
  
  // Mark others as eliminated
  this.tiedCandidates.forEach(candidate => {
    if (candidate.candidateId.toString() !== winnerCandidateId.toString()) {
      candidate.isEliminated = true
    }
  })
  
  return this.save()
}

// Instance method to add vote
tieBreakerSchema.methods.addVote = function(voterId, candidateId, voterMunicipality) {
  // Check if voter already voted
  const existingVoter = this.participatingVoters.find(v => v.voterId.toString() === voterId.toString())
  if (existingVoter) {
    throw new Error('Voter has already participated in this tie-breaker')
  }
  
  // Add voter to participants
  this.participatingVoters.push({
    voterId,
    municipality: voterMunicipality,
    votedAt: new Date(),
  })
  
  // Update vote count for candidate
  const candidate = this.tiedCandidates.find(c => c.candidateId.toString() === candidateId.toString())
  if (candidate) {
    candidate.tieBreakerVotes += 1
  }
  
  // Update total votes cast
  this.totalVotesCast += 1
  
  return this.save()
}

// Instance method to check if voter can vote
tieBreakerSchema.methods.canVoterParticipate = function(voterId) {
  if (this.status !== 'active') return false
  
  const hasVoted = this.participatingVoters.some(v => v.voterId.toString() === voterId.toString())
  return !hasVoted
}

// Instance method to get current results
tieBreakerSchema.methods.getCurrentResults = function() {
  return this.tiedCandidates
    .map(candidate => ({
      candidateId: candidate.candidateId,
      originalVotes: candidate.originalVotes,
      tieBreakerVotes: candidate.tieBreakerVotes,
      totalVotes: candidate.originalVotes + candidate.tieBreakerVotes,
      isWinner: candidate.isWinner,
      isEliminated: candidate.isEliminated,
    }))
    .sort((a, b) => b.tieBreakerVotes - a.tieBreakerVotes)
}

// Static method to create new tie-breaker
tieBreakerSchema.statics.createTieBreaker = function(data) {
  const {
    round,
    tieBreakerRound,
    tiedCandidates,
    tiePosition,
    sessionId,
    createdBy,
    totalVotesRequired,
    deadline,
  } = data
  
  return this.create({
    round,
    tieBreakerRound,
    tiedCandidates: tiedCandidates.map(candidate => ({
      candidateId: candidate.candidateId,
      originalVotes: candidate.votes,
      tieBreakerVotes: 0,
    })),
    tiePosition,
    sessionId,
    createdBy,
    totalVotesRequired,
    deadline,
    description: `Tie-breaker for position ${tiePosition} in round ${round}`,
    status: 'pending',
  })
}

// Static method to get active tie-breaker
tieBreakerSchema.statics.getActiveTieBreaker = function() {
  return this.findOne({ status: 'active' })
    .populate('tiedCandidates.candidateId', 'name municipality')
    .populate('sessionId')
}

// Static method to get tie-breaker history
tieBreakerSchema.statics.getTieBreakerHistory = function(sessionId) {
  return this.find({ sessionId })
    .populate('tiedCandidates.candidateId', 'name municipality')
    .populate('winnerCandidateId', 'name municipality')
    .populate('createdBy', 'username municipality')
    .populate('resolvedBy', 'username municipality')
    .sort({ round: 1, tieBreakerRound: 1 })
}

// Static method to detect if new tie-breaker is needed
tieBreakerSchema.statics.checkForNewTies = async function(round, position = 15) {
  const Vote = mongoose.model('Vote')
  
  // Get vote counts for the round
  const voteCounts = await Vote.getVoteCountsByCandidate(round, 'regular')
  
  if (voteCounts.length < position) return null
  
  // Check if there's a tie at the specified position
  const positionVotes = voteCounts[position - 1]?.voteCount
  if (!positionVotes) return null
  
  // Find all candidates with the same vote count as the position
  const tiedCandidates = voteCounts.filter(candidate => candidate.voteCount === positionVotes)
  
  if (tiedCandidates.length > 1) {
    return {
      position,
      voteCount: positionVotes,
      candidates: tiedCandidates,
    }
  }
  
  return null
}

// Pre-save middleware
tieBreakerSchema.pre('save', function(next) {
  // Auto-complete if all required votes are cast
  if (this.status === 'active' && this.totalVotesCast >= this.totalVotesRequired) {
    // Find winner (candidate with most tie-breaker votes)
    const sortedCandidates = this.tiedCandidates.sort((a, b) => b.tieBreakerVotes - a.tieBreakerVotes)
    
    if (sortedCandidates.length > 0 && sortedCandidates[0].tieBreakerVotes > 0) {
      // Check if there's still a tie
      const topVotes = sortedCandidates[0].tieBreakerVotes
      const winners = sortedCandidates.filter(c => c.tieBreakerVotes === topVotes)
      
      if (winners.length === 1) {
        // We have a clear winner
        this.complete(winners[0].candidateId, winners[0].tieBreakerVotes)
      }
      // If still tied, another tie-breaker round may be needed
    }
  }
  
  next()
})

const TieBreaker = mongoose.model('TieBreaker', tieBreakerSchema)

export default TieBreaker
