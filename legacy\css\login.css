/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body, html {
    font-family: 'Urbanist', Arial, sans-serif;
    background-color: #f7f7f7; 
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh; 
    overflow-x: hidden; 
}
/* Center and responsive container */
.container-nav, .container-footer, .container {
    width: 100%; 
    max-width: 1200px; 
    margin: 0 auto;
    padding: 0 10px; 
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header {
    background: #D3DCE5;
    padding: 25px 0;
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 70px;
    display: block;
    height: auto;
    margin-right: 10px;
}

/* Center logo text */
.logo-text b {
    font-family: 'Urbanist', sans-serif;
    font-size: 18px;
    font-weight: bold;
    color: #243328;
}

/* Navigation styling */
.nav-center {
    display: flex;
    gap: 20px;
}

.nav-button {
    font-size: 14px;
    font-weight: bold;
    color: #243328;
    padding: 10px 15px;
    position: relative;
    text-transform: capitalize;
    letter-spacing: 0.15em;
}

.nav-button:hover {
    background: #d3dce5;
}
a {
    color: #243328;
    position: relative;
    padding: 5px 10px;
    text-transform: uppercase;
    transition: color 0.3s;
}

a:after {
    content: "";
    position: absolute;
    height: 2px;
    left: 50%;
    bottom: 0;
    width: 0;
    background: #243328;
    transition: width 0.3s ease, left 0.3s ease;
}

a:hover:after {
    width: 100%;
    left: 0;
}


/* Login section styles */
.login-section {
    background-image: url('ff/bgg1.png'); /* Same background as content */
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    flex: 1;
    padding: 40px 20px; 
    text-align: center; 
    display: block; 
    justify-content: center;
}

.section-title {
    font-size: clamp(20px, 8vw, 16px);
    margin-top: 12px; 
    margin-bottom: 45px; 
    text-align: center; 
}

/* Footer styling */
.footer {
    background: #fff;
    padding: 5px 0;
    text-align: center; 
}

.container-footer {
    display: flex; 
    justify-content: center; 
    align-items: center; 
    width: 100%; 
}

/* Footer text styling */
.footer-text {
    display: flex; 
    justify-content: center;
    align-items: right; 
    font-size: 12px;
    color: #2d4863;
    margin: 0; 
}


/* Container for login elements */
.login-container {
    max-width: 350px; 
    width: 90%; 
    height: 330px;
   /* background-color: rgba(243, 243, 243, 0.4); */
    background-color: #f0f3f6;
    border-radius: 45px; 
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); 
    margin: 20px auto; 
    margin-top: -30px;
}

/* Additional styles for the form */
form {
    display: flex;
    flex-direction: column; 
    align-items: stretch; 
}

/* Style for labels */
label {
    margin-bottom: 5px; 
    font-weight: bold; 
    font-size: 14px; 
}

/* Input field styling */
input[type="text"], input[type="password"] {
    padding: 10px; 
    margin-bottom: 15px; 
    border: 1px solid #D3DCE5; 
    border-radius: 4px; 
    width: 100%; 
    font-size: 14px; 
    background-color: #D3DCE5;
}

/* Button styling */
input[type="submit"] {
    padding: 10px; 
    background-color: #2F984D; 
    color: white;
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    width: 100%; 
    font-size: 14px; 
    margin-top: 8px;
}

/* Hover effect for button */
input[type="submit"]:hover {
    background-color: #45dd64; 
}

/* Error message styling */
.ermessage {
    color: red; 
    margin-top: 0px; 
    text-align: center; 
    font-size: 12px; 
}

/* Footer text styling */
.footer-text {
    font-size: 12px;
    color: #333; /* Footer text color */
    margin: 0; 
}

/* Media Queries */
@media (max-width: 768px) {
    /* Adjustments for mobile responsiveness */
    .nav-center {
        flex-direction: column; 
        gap: 10px; /* Spacing between nav items */
    }
    .section-title {
        font-size: 20px; 
        margin-top: 20px; /* Adjust top margin for smaller screens */
    }
    .login-container {
        max-width: 300px; 
        width: 90%; 
        height: 250px;
        background-color: rgba(243, 243, 243, 0.9);
        border-radius: 15px; 
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); 
        margin: 20px auto; 
    }
}

@media (max-width: 480px) {
    .logo-container {
        display: flex;
        flex-direction: column; 
        align-items: center;    
        justify-content: center;
        text-align: center;      
        width: 100%;      
        margin-top: 50px;
    }

    .logo {
        width: 70px; 
        height: auto;
        margin-bottom: 10px; 
    }

    /* Increase specificity to ensure it applies */
    .logo-text b {
        display: none; 
    }

    .nav-button {
        display: none; 
    }
}
