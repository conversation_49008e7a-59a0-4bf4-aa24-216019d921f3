<?php
session_start(); // Start the session

// Check if the admin is authenticated
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    // Redirect to the admin login page if not authenticated
    header('Location: admin_login.php');
    exit;
}

// Database connection and vote results retrieval
include 'db_connection.php';

// SQL query to retrieve vote results
$sql = "SELECT c.candidate_name, COUNT(v.vote_id) AS total_votes
        FROM candidates c
        LEFT JOIN votes v ON c.candidate_id = v.candidate_id
        GROUP BY c.candidate_name
        ORDER BY total_votes DESC, c.candidate_name ASC"; // Added c.candidate_name ASC to order alphabetically

$result = $conn->query($sql);

//the top 15
$top_15_candidates = [];
if ($result->num_rows > 0) {
    $count = 1; // Initialize count
    while ($row = $result->fetch_assoc()) {
        $top_15_candidates[] = $row; // Corrected to include the entire row
        $count++;
    }
}
$_SESSION['top_15_candidates'] = $top_15_candidates;

// Close the database connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DFPTA</title>
    <link rel="stylesheet" type="text/css" href="admin_panel.css"> <!-- Link to your CSS file -->
    <link rel="icon" href="dfptalogo.png" type="image/x-icon"> 
</head>
<body>

<header class="header"></header> 

    <nav class="navigation">
        <div class="logo-container">
            <img src="dfptalogo.png" alt="DFPTA Logo" class="logo">
            <h1>DFPTA Online Voting System - SDO Camarines Sur</h1>
        </div>
        <ul class="nav-list">
            <li class="nav-item"><a class="nav-link" href="listfinal.php">Summary of Votes</a></li>
            <li class="nav-item"><a class="nav-link" href="tieverif.php">Tie Verification</a></li>
            <li class="nav-item"><a class="nav-link" href="admin_logout.php">Logout</a></li>
        </ul>
    </nav>

    <div class="top35">
        <a href="top35.php"> Total Votes of 35 Candidates</a>
        <a href="tie_candidates.php"> Total Votes of Tie Candidates</a>
    </div>

</body>
</html>
