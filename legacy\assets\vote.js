// Move the following line outside of the validateForm function to make it accessible globally
var selectedCandidatesContainer = document.getElementById('selectedCandidates');

function validateForm() {
    var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
    var remainingVotes = 15 - checkboxes.length;

    if (remainingVotes < 0) {
        alert("You can vote for a maximum of 15 candidates.");
        return false;
    }

    document.getElementById('remainingVotes').textContent = remainingVotes;

    return true;
}

function limitCheckboxes() {
    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].addEventListener('change', function () {
            var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            if (checkedCheckboxes.length > 15) {
                alert('You can vote for up to 15 candidates only.');
                this.checked = false;
            } else {
                updateSelectedCandidates(); // Call the function to update the selected candidates list
            }
        });
    }
}

function uncheckAll() {
    var checkboxes = document.querySelectorAll('input[type="checkbox"]');

    checkboxes.forEach(function (checkbox) {
        checkbox.checked = false;
    });

    document.getElementById('remainingVotes').textContent = 15;

    updateSelectedCandidates(); // Call the function to update the selected candidates list
}

function updateSelectedCandidates() {
    // Clear previous selections and remove the "highlight" class from all candidates
    selectedCandidatesContainer.innerHTML = '';
    var allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
    allCheckboxes.forEach(function (checkbox) {
        checkbox.nextSibling.classList.remove('highlight');
    });

    // Highlight the checked candidates
    var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
    checkedCheckboxes.forEach(function (checkbox) {
        var candidateLabel = checkbox.nextSibling;
        candidateLabel.classList.add('highlight');

        // Append the highlighted candidate to the selected candidates container
        var listItem = document.createElement('div');
        listItem.textContent = candidateLabel.textContent.trim();
        selectedCandidatesContainer.appendChild(listItem);
    });
}

window.onload = limitCheckboxes; // Ensure the event listeners are set up on page load
