import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Candidate } from '@/services/votingService'
import { Check, MapPin } from 'lucide-react'

interface CandidateCardProps {
  candidate: Candidate
  isSelected: boolean
  onToggle: (candidateId: string) => void
  disabled?: boolean
  showDetails?: boolean
}

export function CandidateCard({
  candidate,
  isSelected,
  onToggle,
  disabled = false,
  showDetails = false,
}: CandidateCardProps) {
  const handleClick = () => {
    if (!disabled) {
      onToggle(candidate._id)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleClick()
    }
  }

  return (
    <Card
      className={cn(
        'relative cursor-pointer transition-all duration-200 hover:shadow-md',
        'focus-within:ring-primary border-2 focus-within:ring-2 focus-within:ring-offset-2',
        isSelected ? 'border-primary bg-accent shadow-md' : 'border-border hover:border-primary/50',
        disabled && 'cursor-not-allowed opacity-50'
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role='button'
      aria-pressed={isSelected}
      aria-label={`${isSelected ? 'Deselect' : 'Select'} ${candidate.municipalityName} (${candidate.district})`}
    >
      {/* Selection indicator */}
      <div
        className={cn(
          'absolute right-3 top-3 flex h-6 w-6 items-center justify-center rounded-full border-2 transition-all',
          isSelected
            ? 'border-primary bg-primary text-primary-foreground'
            : 'border-muted-foreground bg-background'
        )}
      >
        {isSelected && <Check className='h-4 w-4' />}
      </div>

      <CardHeader className='pb-3'>
        <div className='flex items-start space-x-3'>
          {/* Municipality icon */}
          <div className='bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full'>
            <MapPin className='text-primary h-6 w-6' />
          </div>

          <div className='min-w-0 flex-1'>
            <h3 className='truncate text-lg font-semibold leading-tight'>
              {candidate.municipalityName}
            </h3>

            <div className='text-muted-foreground mt-1 flex items-center text-sm'>
              <span className='truncate'>{candidate.district}</span>
            </div>

            {/* Vote count badge if available */}
            {candidate.totalVotes > 0 && showDetails && (
              <Badge variant='secondary' className='mt-2'>
                {candidate.totalVotes} votes
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      {showDetails && (
        <CardContent className='pt-0'>
          {/* Municipality details */}
          <div className='space-y-2'>
            <div className='text-sm'>
              <span className='font-medium'>District:</span> {candidate.district}
            </div>
            {candidate.totalVotes > 0 && (
              <div className='text-sm'>
                <span className='font-medium'>Total Votes:</span> {candidate.totalVotes}
              </div>
            )}
            {candidate.currentRank && (
              <div className='text-sm'>
                <span className='font-medium'>Current Rank:</span> #{candidate.currentRank}
              </div>
            )}
            {candidate.isWinner && (
              <Badge variant='default' className='mt-2'>
                Winner
              </Badge>
            )}
            {candidate.isEliminated && (
              <Badge variant='destructive' className='mt-2'>
                Eliminated
              </Badge>
            )}
          </div>
        </CardContent>
      )}

      {/* Selection overlay for better visual feedback */}
      {isSelected && (
        <div className='bg-primary/5 pointer-events-none absolute inset-0 rounded-lg' />
      )}
    </Card>
  )
}

// Compact version for mobile or dense layouts
export function CandidateCardCompact({
  candidate,
  isSelected,
  onToggle,
  disabled = false,
}: CandidateCardProps) {
  const handleClick = () => {
    if (!disabled) {
      onToggle(candidate._id)
    }
  }

  return (
    <div
      className={cn(
        'flex cursor-pointer items-center rounded-lg border-2 p-3 transition-all',
        'focus-within:ring-primary focus-within:ring-2 focus-within:ring-offset-1 hover:shadow-sm',
        isSelected ? 'border-primary bg-accent' : 'border-border hover:border-primary/50',
        disabled && 'cursor-not-allowed opacity-50'
      )}
      onClick={handleClick}
      role='button'
      tabIndex={disabled ? -1 : 0}
      aria-pressed={isSelected}
    >
      {/* Checkbox */}
      <div
        className={cn(
          'mr-3 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded border-2',
          isSelected
            ? 'border-primary bg-primary text-primary-foreground'
            : 'border-muted-foreground'
        )}
      >
        {isSelected && <Check className='h-3 w-3' />}
      </div>

      {/* Municipality info */}
      <div className='min-w-0 flex-1'>
        <div className='truncate font-medium'>{candidate.municipalityName}</div>
        <div className='text-muted-foreground truncate text-sm'>{candidate.district}</div>
      </div>

      {/* Vote count badge */}
      <div className='ml-2 flex items-center space-x-1'>
        {candidate.totalVotes > 0 && (
          <Badge variant='secondary' className='text-xs'>
            {candidate.totalVotes}
          </Badge>
        )}
        {candidate.isWinner && (
          <Badge variant='default' className='text-xs'>
            Winner
          </Badge>
        )}
      </div>
    </div>
  )
}
