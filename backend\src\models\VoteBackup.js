import mongoose from 'mongoose'

const voteBackupSchema = new mongoose.Schema(
  {
    // Original vote reference
    originalVoteId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Vote',
      required: [true, 'Original vote ID is required'],
      index: true,
    },
    // Backup metadata
    backupType: {
      type: String,
      enum: {
        values: ['reset', 'deletion', 'modification', 'system_backup'],
        message: 'Invalid backup type'
      },
      required: [true, 'Backup type is required'],
    },
    backupReason: {
      type: String,
      required: [true, 'Backup reason is required'],
      trim: true,
      maxlength: [500, 'Backup reason cannot exceed 500 characters'],
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Performed by user ID is required'],
      index: true,
    },
    // Original vote data (preserved)
    originalVoteData: {
      voterId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      candidateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Candidate',
        required: true,
      },
      voteType: {
        type: String,
        enum: ['regular', 'tiebreaker', 'officer'],
        required: true,
      },
      round: {
        type: Number,
        required: true,
      },
      sessionId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'VotingSession',
        required: true,
      },
      voterMunicipality: {
        type: String,
        required: true,
      },
      candidateMunicipality: {
        type: String,
        required: true,
      },
      originalTimestamp: {
        type: Date,
        required: true,
      },
      ipAddress: String,
      userAgent: String,
      batchId: String,
      batchPosition: Number,
      totalBatchSize: Number,
      voteHash: String,
    },
    // Backup audit trail
    backupTimestamp: {
      type: Date,
      default: Date.now,
      index: true,
    },
    backupIpAddress: {
      type: String,
      trim: true,
    },
    backupUserAgent: {
      type: String,
      trim: true,
    },
    // Restoration tracking
    isRestored: {
      type: Boolean,
      default: false,
    },
    restoredAt: {
      type: Date,
    },
    restoredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    restorationNotes: {
      type: String,
      trim: true,
      maxlength: [500, 'Restoration notes cannot exceed 500 characters'],
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v
        delete ret.backupIpAddress
        delete ret.backupUserAgent
        return ret
      },
    },
  }
)

// Indexes for performance
voteBackupSchema.index({ originalVoteId: 1, backupType: 1 })
voteBackupSchema.index({ performedBy: 1, backupTimestamp: -1 })
voteBackupSchema.index({ 'originalVoteData.voterId': 1, backupTimestamp: -1 })
voteBackupSchema.index({ 'originalVoteData.candidateId': 1, backupTimestamp: -1 })
voteBackupSchema.index({ backupType: 1, backupTimestamp: -1 })
voteBackupSchema.index({ isRestored: 1, backupTimestamp: -1 })

// Static method to create backup from vote
voteBackupSchema.statics.createBackup = async function(vote, backupType, backupReason, performedBy, ipAddress = null, userAgent = null) {
  const backup = new this({
    originalVoteId: vote._id,
    backupType,
    backupReason,
    performedBy,
    originalVoteData: {
      voterId: vote.voterId,
      candidateId: vote.candidateId,
      voteType: vote.voteType,
      round: vote.round,
      sessionId: vote.sessionId,
      voterMunicipality: vote.voterMunicipality,
      candidateMunicipality: vote.candidateMunicipality,
      originalTimestamp: vote.timestamp,
      ipAddress: vote.ipAddress,
      userAgent: vote.userAgent,
      batchId: vote.batchId,
      batchPosition: vote.batchPosition,
      totalBatchSize: vote.totalBatchSize,
      voteHash: vote.voteHash,
    },
    backupIpAddress: ipAddress,
    backupUserAgent: userAgent,
  })
  
  return backup.save()
}

// Static method to get backup history for a user
voteBackupSchema.statics.getUserBackupHistory = function(userId, limit = 50) {
  return this.find({ 'originalVoteData.voterId': userId })
    .populate('performedBy', 'username municipality role')
    .populate('originalVoteData.candidateId', 'municipalityName district')
    .sort({ backupTimestamp: -1 })
    .limit(limit)
}

// Static method to get backup statistics
voteBackupSchema.statics.getBackupStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$backupType',
        count: { $sum: 1 },
        latestBackup: { $max: '$backupTimestamp' },
      },
    },
    {
      $sort: { count: -1 },
    },
  ])
  
  const totalBackups = await this.countDocuments()
  const restoredBackups = await this.countDocuments({ isRestored: true })
  
  return {
    totalBackups,
    restoredBackups,
    backupsByType: stats,
  }
}

// Instance method to restore backup
voteBackupSchema.methods.restore = async function(restoredBy, restorationNotes = '') {
  const Vote = mongoose.model('Vote')
  const Candidate = mongoose.model('Candidate')
  
  // Create new vote from backup data
  const restoredVote = new Vote({
    ...this.originalVoteData,
    timestamp: new Date(), // New timestamp for restoration
  })
  
  await restoredVote.save()
  
  // Update candidate vote count
  const candidate = await Candidate.findById(this.originalVoteData.candidateId)
  if (candidate) {
    await candidate.addVoteForRound(this.originalVoteData.round, this.originalVoteData.voteType, 1)
  }
  
  // Mark backup as restored
  this.isRestored = true
  this.restoredAt = new Date()
  this.restoredBy = restoredBy
  this.restorationNotes = restorationNotes
  
  return this.save()
}

const VoteBackup = mongoose.model('VoteBackup', voteBackupSchema)

export default VoteBackup
