<?php
session_start();

// Check if the admin is authenticated
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    // Redirect to the admin login page if not authenticated
    header('Location: admin_login.php');
    exit;
}

// Database connection
include 'db_connection.php';

// Get tied candidates from the session
if (!isset($_SESSION['top_15_candidates'])) {
    header('Location: admin_panel.php');
    exit;
}

$candidates = $_SESSION['top_15_candidates'];

usort($candidates, function ($a, $b) {
    return $b['total_votes'] - $a['total_votes'];
});

$minVotes15 = $candidates[14]['total_votes'];
$tieCandidates15 = array_filter($candidates, function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
});

$tiedCandidates = count($tieCandidates15) > 1 ? array_values($tieCandidates15) : [];

// Generate a comma-separated list of tied candidate names for the SQL query
$tiedCandidateNames = implode("', '", array_column($tiedCandidates, 'candidate_name'));

// SQL query to retrieve vote results only for tied candidates
$sql = "SELECT c.candidate_name, COUNT(tv.vote_id) AS total_votes
        FROM candidates c
        LEFT JOIN tie_votes tv ON c.candidate_id = tv.candidate_id
        WHERE c.candidate_name IN ('$tiedCandidateNames')
        GROUP BY c.candidate_name
        ORDER BY total_votes DESC, c.candidate_name ASC";

$result = $conn->query($sql);

// The tied candidates
$tied_candidates = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $tied_candidates[] = $row; // Corrected to include the entire row
    }
}

// Close the database connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tie Result - Online Voting System</title>
    <link rel="stylesheet" type="text/css" href="admin_panel.css"> <!-- Link to your CSS file -->
</head>
<body>
    <header>
        <h1>Tie Result - Online Voting System</h1>
    </header>

    <nav>
        <ul>
            <li><a href="admin_logout.php">Logout</a></li> <!-- Link to the admin logout page -->
            <li><a href="listfinal.php">Summary of Votes</a></li> <!-- Link to the final result page -->
        </ul>
    </nav>

    <section>
        <div class="header-row">
            <h2>Tie Result - DFPTA Election 2023</h2>
        </div>
        <table>
            <tr>
                <th>No</th>
                <th>Candidate Name</th>
                <th>Total Votes</th>
            </tr>
            <?php
            if (!empty($tied_candidates)) {
                $count = 1;
                foreach ($tied_candidates as $row) {
                    echo "<tr>";
                    echo "<td>$count</td>";
                    echo "<td>" . $row["candidate_name"] . "</td>";
                    echo "<td>" . $row["total_votes"] . "</td>";
                    echo "</tr>";
                    $count++;
                }
            } else {
                echo "No results available yet.";
            }
            ?>
        </table>
    </section>

    <footer>
        <div class="footer-row">
            <p>&copy; SOCMOB</p>
        </div>
    </footer>
</body>
</html>
