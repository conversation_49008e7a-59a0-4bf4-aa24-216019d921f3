body {
    font-family: 'Urbanist', sans-serif;
    background-color: #f0f0f0;
    text-align: center;
    margin: 0;
    padding: 0;
}
/* Center and responsive container */
.container-nav, .container-footer, .container {
    width: 100%; 
    max-width: 1200px; 
    margin: 0 auto;
    padding: 0 10px; 
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header {
    background: #D3DCE5;
    padding: 25px 0;
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 70px;
    display: block;
    height: auto;
    margin-right: 10px;
}

/* Center logo text */
.logo-text b {
    font-family: 'Urbanist', sans-serif;
    font-size: 18px;
    font-weight: bold;
    color: #243328;
}

/* Navigation styling */
.nav-center {
    display: flex;
    gap: 20px;
}

.nav-button {
    font-size: 14px;
    font-weight: bold;
    color: #2d4863;
    padding: 10px 15px;
    position: relative;
    text-transform: capitalize;
    letter-spacing: 0.15em;
}

.nav-button:hover {
    background: #d3dce5;
}

/* Content styling */
.content {
    background-image: url('ff/bgg1.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
    color: #2D4863;
    padding: 10px 0;
    width: 100%;
    display: flex;
    justify-content: center; 
    align-items: center; 
    flex: 1; 
    text-align: center; 
    flex-direction: column; 
}



form {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    margin: 40px;
    text-align: center; /* Center the text horizontally */
} 
.form-section {
    background-image: url('ff/bgg1.png');
    background-size: cover; /* Use 'cover' to maintain aspect ratio and cover the entire container */
    background-repeat: no-repeat; /* Prevent the background from repeating */
    background-position: center; /* Center the background image */
    flex: 1;
    padding: 0; /* Adjust padding for responsiveness */
    text-align: center;
    margin-top: 0;/*40px*/
}

/* Navigation styles */
.nav-center {
    display: flex;
    gap: 30px;
    font-size: 14px;
}

.nav-button {
    font-family: 'Urbanist', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #243328;
    text-transform: uppercase;
    text-decoration: none;
    letter-spacing: 0.15em;
    padding: 15px 20px;
    position: relative;
    transition: background 0.3s;
}


a {
    color: #243328;
    position: relative;
    padding: 5px 10px;
    text-transform: uppercase;
    transition: color 0.3s;
}

a:after {
    content: "";
    position: absolute;
    height: 2px;
    left: 50%;
    bottom: 0;
    width: 0;
    background: #243328;
    transition: width 0.3s ease, left 0.3s ease;
}

a:hover:after {
    width: 100%;
    left: 0;
}

.candidates {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 5px;
    margin: 10px auto;
    align-items: flex-start;
    justify-content: start;
}

input[type="checkbox"] {
    margin-bottom: 5px;
    margin-right: 5px; /* Adjust this margin to control the space between the checkbox and text */
}

/* Align text to the left within each checkbox */
.candidates > div {
    font-family: 'Urbanist', sans-serif;
    display: flex;
    align-items: center;
    padding: 5px;
    border: none; /* Remove borders */
    text-align: left; /* Justify text to the left */
}

/* Style the "Vote" button */
input[type="submit"] {
    background-color: #26704C;
    color: white;
    font-weight: bold;
    padding: 10px 30px;
    margin-top: 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    display: block; /* Set display to block */
    margin: 0 auto; /* Center the button horizontally */
    
}

input[type="submit"]:hover {
    background-color: #45dd64;
}


.form-container {
    justify-content: right; /* Center the content vertically */
    display: flex;
    flex-direction: column; /* Stack child elements vertically */
    align-items: center; /* Center the content horizontally */
    height: 100vh; /* Set the container height to 100% of the viewport height */
}

.vote-button {
    margin-top: 20px; /* Add some top margin for spacing */
}

/* Add this style to position the selected candidates on the left side */
.selected-candidates {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.selected-candidates h2 {
    background-color: #243328;
    color: white;
    padding: 10px;
    margin: 0;
    border-radius: 10px 10px 0 0;
}

.highlight {
    background-color: #ffffcc; /* Change the background color as needed */
    font-weight: bold; /* Optionally, make the text bold */
}