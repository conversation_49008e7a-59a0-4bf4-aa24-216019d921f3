<?php
include 'db_connection.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Fetch candidate information from execom_members table
$sql_candidates = "SELECT candidate_id, candidate_name FROM execom_members";
$result_candidates = $conn->query($sql_candidates);

$candidates = [];
while ($row = $result_candidates->fetch_assoc()) {
    $candidates[$row['candidate_id']] = $row['candidate_name'];
}

// Fetch vote counts from the chairperson table
$sql_votes = "SELECT candidate_id, COUNT(*) as vote_count FROM chairperson GROUP BY candidate_id";
$result_votes = $conn->query($sql_votes);

$voteCounts = [];

// Initialize vote counts for all candidates to 0
foreach ($candidates as $candidateId => $candidateName) {
    $voteCounts[$candidateId] = 0;
}

while ($row = $result_votes->fetch_assoc()) {
    $voteCounts[$row['candidate_id']] = $row['vote_count'];
}

// Close the database connection
$conn->close();

// Sort candidates based on vote counts (descending order)
arsort($voteCounts);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chairperson Election Results</title>
    <link rel="stylesheet" type="text/css" href="admin_panel.css"> <!-- Link to your CSS file -->
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        section {
            display: flex;
            justify-content: space-between;
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
        }

        .column {
            width: 30%;
            box-sizing: border-box;
            margin-right: 20px; /* Adjust the margin as needed */
        }

        table {
            width: 100%;
            margin-bottom: 20px; /* Add margin between tables */
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #fff;
        }

        button {
            margin-top: 20px;
            padding: 10px;
            background-color: #4CAF50;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <header>
        <h1>Chairperson Election Results</h1>
    </header>

    <nav>
        <ul>
            <li><a href="admin_panel.php">Admin Vote Panel</a></li> <!-- Link to the admin panel page -->
        </ul>
    </nav>

    <section>
        <div class="column">
            <table>
                <thead>
                    <tr>
                        <th>Candidate</th>
                        <th>Votes</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($voteCounts as $candidateId => $votes) : ?>
                        <tr>
                            <td><?php echo isset($candidates[$candidateId]) ? $candidates[$candidateId] : 'Candidate Not Found'; ?></td>
                            <td><?php echo $votes; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- Add a button to redirect to admin_panel.php -->
            <button onclick="window.location.href='admin_panel.php'">Go to Admin Panel</button>
        </div>
    </section>

    <footer>
        <div class="footer-row">
            <p>&copy; SOCMOB</p>
        </div>
    </footer>
</body>
</html>
