import asyncHandler from 'express-async-handler'
import mongoose from 'mongoose'
import Candidate from '../models/Candidate.js'
import User from '../models/User.js'
import Vote from '../models/Vote.js'
import VotingSession from '../models/VotingSession.js'
import logger from '../utils/logger.js'

// @desc    Get all active candidates
// @route   GET /api/candidates
// @access  Private (voter, admin, execom, tie-breaker)
export const getCandidates = asyncHandler(async (req, res) => {
  const candidates = await Candidate.find({ isActive: true })
    .select(
      'municipalityName district totalVotes currentRank finalPosition isWinner isEliminated isActive'
    )
    .sort({ municipalityName: 1 })

  res.status(200).json({
    success: true,
    data: {
      candidates,
      count: candidates.length,
    },
  })
})

// @desc    Get voting status for current user
// @route   GET /api/vote/status
// @access  Private (voter, admin, execom, tie-breaker)
export const getVotingStatus = asyncHandler(async (req, res) => {
  const userId = req.user.id
  const user = await User.findById(userId)

  if (!user) {
    res.status(404)
    throw new Error('User not found')
  }

  // Get current voting session
  const currentSession = await VotingSession.getCurrentSession()

  if (!currentSession) {
    return res.status(200).json({
      success: true,
      data: {
        canVote: false,
        hasVoted: false,
        votingStatus: 'no-active-session',
        message: 'No active voting session',
      },
    })
  }

  // Check if user has voted (simplified for single-round voting)
  const hasVoted = user.hasVoted

  res.status(200).json({
    success: true,
    data: {
      canVote: currentSession.status === 'active' && !hasVoted,
      hasVoted: hasVoted,
      votingStatus: currentSession.status,
      sessionId: currentSession._id,
      maxCandidatesPerVote: currentSession.maxCandidatesPerVote,
      user: {
        municipality: user.municipality,
        role: user.role,
        lastVotedAt: user.lastVotedAt,
      },
    },
  })
})

// @desc    Submit votes for candidates
// @route   POST /api/vote
// @access  Private (voter, admin, execom, tie-breaker)
export const submitVotes = asyncHandler(async (req, res) => {
  const { candidateIds } = req.body
  const userId = req.user.id
  const userAgent = req.get('User-Agent')
  const ipAddress = req.ip

  // Validation
  if (!candidateIds || !Array.isArray(candidateIds) || candidateIds.length === 0) {
    res.status(400)
    throw new Error('Please select at least one candidate')
  }

  // Get current voting session
  const currentSession = await VotingSession.getCurrentSession()

  if (!currentSession || currentSession.status !== 'active') {
    res.status(400)
    throw new Error('No active voting session or voting is not currently allowed')
  }

  // Check maximum candidates limit
  if (candidateIds.length > currentSession.maxCandidatesPerVote) {
    res.status(400)
    throw new Error(`You can select a maximum of ${currentSession.maxCandidatesPerVote} candidates`)
  }

  // Get user details
  const user = await User.findById(userId)
  if (!user) {
    res.status(404)
    throw new Error('User not found')
  }

  // Check if user has already voted
  if (user.hasVoted) {
    res.status(400)
    throw new Error('You have already voted')
  }

  // Validate all candidate IDs exist and are active
  const candidates = await Candidate.find({
    _id: { $in: candidateIds },
    isActive: true,
  })

  if (candidates.length !== candidateIds.length) {
    res.status(400)
    throw new Error('One or more selected candidates are invalid or inactive')
  }

  // Check for duplicate candidate IDs
  const uniqueCandidateIds = [...new Set(candidateIds)]
  if (uniqueCandidateIds.length !== candidateIds.length) {
    res.status(400)
    throw new Error('Duplicate candidate selections are not allowed')
  }

  // Start transaction
  const session = await mongoose.startSession()
  session.startTransaction()

  try {
    // Generate batch ID for this vote submission
    const batchId = new mongoose.Types.ObjectId().toString()

    // Create vote records
    const votes = candidateIds.map((candidateId, index) => ({
      voterId: userId,
      candidateId,
      voteType: 'regular',
      round: currentRound,
      sessionId: currentSession._id,
      voterMunicipality: user.municipality,
      candidateMunicipality: candidates.find(c => c._id.toString() === candidateId.toString())
        .municipality,
      ipAddress,
      userAgent,
      batchId,
      batchPosition: index + 1,
      totalBatchSize: candidateIds.length,
    }))

    // Insert votes
    const insertedVotes = await Vote.insertMany(votes, { session })

    // Update candidate vote counts
    await Promise.all(
      candidates.map(candidate => candidate.addVoteForRound(currentRound, 'regular', 1))
    )

    // Update user voting status
    user.votingRounds.hasVotedInRound[`round${currentRound}`] = true
    user.votingRounds.lastVotedRound = currentRound
    user.votingRounds.lastVotedAt = new Date()
    user.hasVoted = true
    await user.save({ session })

    // Update session statistics
    currentSession.votesSubmitted += 1
    await currentSession.updateStatistics()
    await currentSession.save({ session })

    // Add audit log
    currentSession.addAuditLog(
      'vote_submitted',
      userId,
      {
        candidateCount: candidateIds.length,
        batchId,
        round: currentRound,
      },
      ipAddress
    )

    await session.commitTransaction()

    logger.info(
      `User ${user.municipality} submitted ${candidateIds.length} votes in round ${currentRound}`
    )

    res.status(201).json({
      success: true,
      data: {
        message: 'Votes submitted successfully',
        votesCount: insertedVotes.length,
        batchId,
        round: currentRound,
        submittedAt: new Date(),
      },
    })
  } catch (error) {
    await session.abortTransaction()
    logger.error('Vote submission failed:', error.message)
    throw error
  } finally {
    session.endSession()
  }
})

// @desc    Get user's voting history
// @route   GET /api/vote/history
// @access  Private (voter, admin, execom, tie-breaker)
export const getVotingHistory = asyncHandler(async (req, res) => {
  const userId = req.user.id

  const votes = await Vote.find({ voterId: userId })
    .populate('candidateId', 'name municipality')
    .populate('sessionId', 'name currentRound status')
    .sort({ timestamp: -1 })

  // Group votes by session and round
  const groupedVotes = votes.reduce((acc, vote) => {
    const key = `${vote.sessionId._id}-${vote.round}-${vote.voteType}`
    if (!acc[key]) {
      acc[key] = {
        sessionId: vote.sessionId._id,
        sessionName: vote.sessionId.name,
        round: vote.round,
        voteType: vote.voteType,
        batchId: vote.batchId,
        timestamp: vote.timestamp,
        candidates: [],
      }
    }
    acc[key].candidates.push({
      id: vote.candidateId._id,
      name: vote.candidateId.name,
      municipality: vote.candidateId.municipality,
    })
    return acc
  }, {})

  res.status(200).json({
    success: true,
    data: {
      votingHistory: Object.values(groupedVotes),
      totalVotes: votes.length,
    },
  })
})

// @desc    Get current voting results (anonymized)
// @route   GET /api/results
// @access  Public
export const getResults = asyncHandler(async (req, res) => {
  const { round = 1, detailed = false } = req.query

  // Get current session
  const currentSession = await VotingSession.getCurrentSession()

  if (!currentSession) {
    return res.status(200).json({
      success: true,
      data: {
        message: 'No active voting session',
        results: [],
      },
    })
  }

  // Get vote counts
  const voteCounts = await Vote.getVoteCountsByCandidate(parseInt(round), 'regular')

  // Get voting statistics
  const stats = await Vote.getVotingStatistics(parseInt(round), 'regular')
  const participationStats = await Vote.getVoterParticipation(parseInt(round))

  // Format results
  const results = voteCounts.map((result, index) => ({
    rank: index + 1,
    candidateId: detailed ? result._id : null,
    candidateName: result.candidate.name,
    municipality: result.candidate.municipality,
    voteCount: result.voteCount,
    percentage: stats.length > 0 ? ((result.voteCount / stats[0].totalVotes) * 100).toFixed(2) : 0,
  }))

  res.status(200).json({
    success: true,
    data: {
      results,
      statistics: {
        totalVotes: stats.length > 0 ? stats[0].totalVotes : 0,
        uniqueVoters: stats.length > 0 ? stats[0].uniqueVoterCount : 0,
        participatingMunicipalities: stats.length > 0 ? stats[0].participatingMunicipalities : 0,
        participationRate: currentSession.currentParticipationRate,
      },
      session: {
        id: currentSession._id,
        name: currentSession.name,
        status: currentSession.status,
        round: parseInt(round),
        maxCandidatesPerVote: currentSession.maxCandidatesPerVote,
      },
      participationByMunicipality: participationStats,
    },
  })
})
