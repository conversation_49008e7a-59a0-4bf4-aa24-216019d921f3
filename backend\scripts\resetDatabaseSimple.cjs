const mongoose = require('mongoose')
const bcrypt = require('bcryptjs')

// District mapping
const DISTRICT_MAPPING = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lupi', 'Ragay', 'Sipocot'],
  '2nd District': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Minalabac', 'Pamplona', 'Pasacao', 'San Fernando'],
  '3rd District': ['Bombon', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': ['Caramoan', 'Garchitorena', 'Goa', 'Lagonoy', 'Parubcan', 'Sa<PERSON><PERSON>', 'San Jose', 'Siruma', 'Tigaon', 'Tinambac'],
  '5th District': ['Baao', 'Balatan', 'Bato', 'Buhi', 'Bula', 'Nabua']
}

// Function to get district for municipality
function getDistrictForMunicipality(municipality) {
  for (const [district, municipalities] of Object.entries(DISTRICT_MAPPING)) {
    if (municipalities.includes(municipality)) {
      return district
    }
  }
  throw new Error(`Municipality ${municipality} not found in any district`)
}

// Function to generate secure password
function generateSecurePassword(municipality) {
  const municipalityPrefix = municipality.toLowerCase().substring(0, 4).padEnd(4, 'x')
  const symbols = ['#', '@', '$', '%', '&', '*', '+', '=']
  const symbol = symbols[Math.floor(Math.random() * symbols.length)]
  
  const numbers = []
  for (let i = 0; i < 3; i++) {
    numbers.push(Math.floor(Math.random() * 10))
  }
  
  return `${municipalityPrefix}${symbol}${numbers.join('')}`
}

async function main() {
  try {
    console.log('🔄 Starting complete database reset...')
    
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/dfpta_voting')
    console.log('✅ Connected to MongoDB')

    // Get collections
    const db = mongoose.connection.db
    const usersCollection = db.collection('users')
    const candidatesCollection = db.collection('candidates')

    // Drop existing data
    await usersCollection.deleteMany({})
    await candidatesCollection.deleteMany({})
    console.log('🗑️ Cleared existing users and candidates')

    const allCredentials = {
      admin: null,
      municipalities: [],
      execom: [],
      tiebreaker: null
    }

    // Create admin user
    console.log('👑 Creating admin user...')
    const adminPassword = 'socmob123'
    const hashedAdminPassword = await bcrypt.hash(adminPassword, 12)
    
    const adminUser = {
      username: 'admin',
      municipality: 'Pili',
      district: '3rd District',
      password: hashedAdminPassword,
      role: 'admin',
      email: '<EMAIL>',
      isActive: true,
      hasVoted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    await usersCollection.insertOne(adminUser)
    allCredentials.admin = {
      username: 'admin',
      municipality: 'Pili',
      district: '3rd District',
      password: adminPassword,
      role: 'admin'
    }
    console.log('✅ Admin user created')

    // Create municipality users
    console.log('🏛️ Creating municipality users...')
    const allMunicipalities = Object.values(DISTRICT_MAPPING).flat()
    
    for (const municipality of allMunicipalities) {
      const district = getDistrictForMunicipality(municipality)
      const securePassword = generateSecurePassword(municipality)
      const hashedPassword = await bcrypt.hash(securePassword, 12)
      
      const municipalityUser = {
        username: municipality.toLowerCase().replace(/\s+/g, ''),
        municipality: municipality,
        district: district,
        password: hashedPassword,
        role: 'voter',
        isActive: true,
        hasVoted: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      await usersCollection.insertOne(municipalityUser)
      allCredentials.municipalities.push({
        municipality: municipality,
        username: municipalityUser.username,
        district: district,
        password: securePassword
      })
    }
    console.log(`✅ Created ${allMunicipalities.length} municipality users`)

    // Create execom users
    console.log('👥 Creating execom users...')
    const execomUsers = [
      {
        username: 'execom_pili',
        municipality: 'Pili',
        district: '3rd District',
        password: 'pili#123',
        role: 'execom',
        email: '<EMAIL>'
      },
      {
        username: 'execom_nabua',
        municipality: 'Nabua',
        district: '5th District',
        password: 'nabu@456',
        role: 'execom',
        email: '<EMAIL>'
      }
    ]

    for (const execomData of execomUsers) {
      const hashedPassword = await bcrypt.hash(execomData.password, 12)
      const execomUser = {
        ...execomData,
        password: hashedPassword,
        isActive: true,
        hasVoted: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      await usersCollection.insertOne(execomUser)
      allCredentials.execom.push({
        username: execomData.username,
        municipality: execomData.municipality,
        district: execomData.district,
        password: execomData.password,
        role: execomData.role
      })
    }
    console.log('✅ Created execom users')

    // Create tie-breaker user
    console.log('⚖️ Creating tie-breaker user...')
    const tiebreakerPassword = 'pili$789'
    const hashedTiebreakerPassword = await bcrypt.hash(tiebreakerPassword, 12)
    
    const tiebreakerUser = {
      username: 'tiebreaker',
      municipality: 'Pili',
      district: '3rd District',
      password: hashedTiebreakerPassword,
      role: 'tie-breaker',
      email: '<EMAIL>',
      isActive: true,
      hasVoted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    await usersCollection.insertOne(tiebreakerUser)
    allCredentials.tiebreaker = {
      username: 'tiebreaker',
      municipality: 'Pili',
      district: '3rd District',
      password: tiebreakerPassword,
      role: 'tie-breaker'
    }
    console.log('✅ Tie-breaker user created')

    // Create municipality candidates
    console.log('🗳️ Creating municipality candidates...')
    for (const municipality of allMunicipalities) {
      const district = getDistrictForMunicipality(municipality)
      const candidate = {
        municipalityName: municipality,
        district: district,
        totalVotes: 0,
        isActive: true,
        votesByRound: [],
        currentRank: null,
        finalPosition: null,
        isWinner: false,
        isEliminated: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      await candidatesCollection.insertOne(candidate)
    }
    console.log(`✅ Created ${allMunicipalities.length} municipality candidates`)

    // Validate creation
    const userCount = await usersCollection.countDocuments()
    const candidateCount = await candidatesCollection.countDocuments()
    
    console.log('\n📊 VALIDATION RESULTS:')
    console.log(`Total Users: ${userCount}`)
    console.log(`Total Candidates: ${candidateCount}`)
    
    if (userCount !== 39) throw new Error(`Expected 39 users, got ${userCount}`)
    if (candidateCount !== 35) throw new Error(`Expected 35 candidates, got ${candidateCount}`)
    
    console.log('✅ All validation checks passed!')
    
    // Print credentials summary
    console.log('\n🔑 CREDENTIALS SUMMARY:')
    console.log(`Admin: ${allCredentials.admin.username} / ${allCredentials.admin.password}`)
    console.log(`Municipality Users: ${allCredentials.municipalities.length}`)
    console.log(`ExeCom Users: ${allCredentials.execom.length}`)
    console.log(`Tie-breaker: ${allCredentials.tiebreaker.username} / ${allCredentials.tiebreaker.password}`)
    
    console.log('\n🎉 Database reset completed successfully!')
    
    // Save credentials to file for documentation
    const fs = require('fs')
    fs.writeFileSync('credentials-output.json', JSON.stringify(allCredentials, null, 2))
    console.log('💾 Credentials saved to credentials-output.json')
    
    return allCredentials
    
  } catch (error) {
    console.error('❌ Database reset failed:', error.message)
    process.exit(1)
  } finally {
    await mongoose.connection.close()
    console.log('🔌 Database connection closed')
  }
}

main()
