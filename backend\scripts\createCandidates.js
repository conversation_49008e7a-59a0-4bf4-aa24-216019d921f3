#!/usr/bin/env node

import dotenv from 'dotenv'
import mongoose from 'mongoose'
import Candidate from '../src/models/Candidate.js'
import logger from '../src/utils/logger.js'

// Load environment variables
dotenv.config()

// Sample candidates data
const candidatesData = [
  {
    name: '<PERSON>',
    municipality: '<PERSON>li',
    description: 'Experienced educator with 15 years in public service',
    position: 'chairperson',
    isExecomMember: true,
    qualifications: [
      'Master of Arts in Educational Management',
      'Licensed Professional Teacher',
      'Certified Public Administrator',
    ],
    experience: [
      {
        title: 'School Principal',
        organization: 'Pili Central Elementary School',
        duration: '2015-2023',
        description: 'Led school improvement initiatives and teacher development programs',
      },
    ],
    platform: 'Committed to enhancing educational quality and teacher welfare across Camarines Sur',
    contactInfo: {
      email: '<EMAIL>',
      phone: '+63 ************',
    },
  },
  {
    name: '<PERSON>',
    municipality: 'Nabua',
    description: 'Veteran teacher and union leader',
    position: 'vice-chairperson',
    isExecomMember: true,
    qualifications: [
      'Bachelor of Elementary Education',
      'Master of Arts in Teaching',
      'Leadership Training Certificate',
    ],
    experience: [
      {
        title: 'Master Teacher II',
        organization: 'Nabua National High School',
        duration: '2010-2023',
        description: 'Mentored new teachers and led curriculum development',
      },
    ],
    platform: 'Advocating for better working conditions and professional development opportunities',
    contactInfo: {
      email: '<EMAIL>',
      phone: '+63 ************',
    },
  },
  {
    name: 'Ana Rodriguez',
    municipality: 'Libmanan',
    description: 'Administrative specialist and education advocate',
    position: 'secretary',
    isExecomMember: true,
    qualifications: [
      'Bachelor of Science in Education',
      'Certificate in Educational Administration',
      'Records Management Specialist',
    ],
    experience: [
      {
        title: 'Administrative Officer',
        organization: 'DepEd Camarines Sur Division',
        duration: '2012-2023',
        description: 'Managed personnel records and administrative processes',
      },
    ],
    platform: 'Ensuring transparent and efficient administrative processes',
    contactInfo: {
      email: '<EMAIL>',
      phone: '+63 ************',
    },
  },
  {
    name: 'Roberto Garcia',
    municipality: 'Baao',
    description: 'Financial management expert and educator',
    position: 'treasurer',
    isExecomMember: true,
    qualifications: [
      'Bachelor of Science in Accounting',
      'Certified Public Accountant',
      'Master in Business Administration',
    ],
    experience: [
      {
        title: 'Budget Officer',
        organization: 'DepEd Regional Office V',
        duration: '2008-2023',
        description: 'Managed budget allocation and financial reporting',
      },
    ],
    platform: 'Promoting fiscal responsibility and transparent financial management',
    contactInfo: {
      email: '<EMAIL>',
      phone: '+63 ************',
    },
  },
]

// Generate additional regular candidates from different municipalities
const municipalities = [
  'Balatan',
  'Bato',
  'Bombon',
  'Buhi',
  'Bula',
  'Cabusao',
  'Calabanga',
  'Camaligan',
  'Canaman',
  'Caramoan',
  'Del Gallego',
  'Gainza',
  'Garchitorena',
  'Goa',
  'Lagonoy',
  'Libmanan',
  'Lupi',
  'Magarao',
  'Milaor',
  'Minalabac',
  'Nabua',
  'Ocampo',
  'Pamplona',
  'Pasacao',
  'Parubcan',
  'Ragay',
  'Sagnay',
  'San Fernando',
  'San Jose',
  'Sipocot',
  'Siruma',
  'Tigaon',
  'Tinambac',
]

const firstNames = [
  'Carlos',
  'Elena',
  'Miguel',
  'Sofia',
  'Rafael',
  'Carmen',
  'Luis',
  'Isabel',
  'Diego',
  'Patricia',
]
const lastNames = [
  'Reyes',
  'Morales',
  'Castillo',
  'Herrera',
  'Jimenez',
  'Vargas',
  'Romero',
  'Gutierrez',
  'Ortiz',
  'Mendoza',
]

// Generate additional candidates
municipalities.forEach((municipality, index) => {
  if (index < 16) {
    // Create 16 more candidates for a total of 20
    const firstName = firstNames[index % firstNames.length]
    const lastName = lastNames[index % lastNames.length]

    candidatesData.push({
      name: `${firstName} ${lastName}`,
      municipality: municipality,
      description: `Dedicated educator from ${municipality} with passion for student development`,
      position: 'candidate',
      isExecomMember: false,
      qualifications: [
        'Bachelor of Elementary/Secondary Education',
        'Licensed Professional Teacher',
        'Various professional development certificates',
      ],
      experience: [
        {
          title: 'Teacher III',
          organization: `${municipality} Public School`,
          duration: '2010-2023',
          description: 'Committed to quality education and student success',
        },
      ],
      platform: `Representing the interests of educators in ${municipality}`,
      contactInfo: {
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@deped.gov.ph`,
        phone: `+63 917 ${String(index + 100).padStart(3, '0')} ${String(index + 1000).padStart(4, '0')}`,
      },
    })
  }
})

async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/dfpta_voting'
    await mongoose.connect(mongoURI)
    logger.info(`Connected to MongoDB: ${mongoURI}`)
  } catch (error) {
    logger.error('MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

async function createCandidates() {
  try {
    // Check if candidates already exist
    const existingCandidates = await Candidate.countDocuments()
    if (existingCandidates > 0) {
      logger.info(`${existingCandidates} candidates already exist. Skipping creation.`)
      return
    }

    // Create candidates
    const candidates = await Candidate.insertMany(candidatesData)
    logger.info(`Created ${candidates.length} candidates successfully`)

    // Log created candidates
    candidates.forEach(candidate => {
      logger.info(
        `Created candidate: ${candidate.name} (${candidate.municipality}) - ${candidate.position}`
      )
    })
  } catch (error) {
    logger.error('Error creating candidates:', error.message)
    logger.error('Full error:', error)
    throw error
  }
}

async function main() {
  try {
    logger.info('Starting candidate creation...')

    await connectDB()
    await createCandidates()

    logger.info('Candidate creation completed successfully!')
  } catch (error) {
    logger.error('Candidate creation failed:', error.message)
    process.exit(1)
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Run the script
main()
