import { useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'

const HomePage = () => {
  const { user, isLoading } = useAuth()
  const navigate = useNavigate()

  // Redirect authenticated users to their role-specific dashboards
  useEffect(() => {
    if (!isLoading && user) {
      switch (user.role) {
        case 'admin':
          navigate('/admin', { replace: true })
          break
        case 'execom':
          navigate('/execom', { replace: true })
          break
        case 'voter':
          navigate('/vote', { replace: true })
          break
        case 'tie-breaker':
          navigate('/vote', { replace: true })
          break
        default:
          // Unknown role, stay on homepage
          break
      }
    }
  }, [user, isLoading, navigate])

  return (
    <div className='min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50'>
      <div className='mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8'>
        <div className='text-center'>
          {/* Hero Section */}
          <div className='mb-12'>
            <h1 className='mb-6 font-heading text-4xl font-bold text-gray-900 md:text-6xl'>
              DFPTA E-Voting System
            </h1>
            <p className='mx-auto mb-8 max-w-3xl text-xl text-gray-600'>
              Secure, transparent, and efficient online voting system for the Department of
              Education - Camarines Sur
            </p>

            <Link to='/login' className='btn-primary px-8 py-3 text-lg'>
              Login to Vote
            </Link>
          </div>

          {/* Features */}
          <div className='mb-12 grid gap-8 md:grid-cols-3'>
            <div className='card text-center'>
              <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100'>
                <svg
                  className='h-6 w-6 text-primary-600'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
              </div>
              <h3 className='mb-2 text-xl font-semibold'>Secure Voting</h3>
              <p className='text-gray-600'>
                Advanced security measures ensure your vote is protected and counted accurately.
              </p>
            </div>

            <div className='card text-center'>
              <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-success-100'>
                <svg
                  className='h-6 w-6 text-success-600'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 10V3L4 14h7v7l9-11h-7z'
                  />
                </svg>
              </div>
              <h3 className='mb-2 text-xl font-semibold'>Real-time Results</h3>
              <p className='text-gray-600'>
                View live voting results and statistics as they happen.
              </p>
            </div>

            <div className='card text-center'>
              <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-warning-100'>
                <svg
                  className='h-6 w-6 text-warning-600'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
                  />
                </svg>
              </div>
              <h3 className='mb-2 text-xl font-semibold'>Easy to Use</h3>
              <p className='text-gray-600'>
                Simple and intuitive interface designed for all users.
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div className='flex justify-center space-x-4'>
            <Link to='/results' className='btn-outline'>
              View Results
            </Link>
            <a href='#contact' className='btn-outline'>
              Contact Support
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
