<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $username = $_POST["username"];
    $password = $_POST["password"];

    $userMappings = [
        "Baao" => 1,
        "Balatan" => 2,
        "Bato" => 3,
        "Bombon" => 4,
        "Buhi" => 5,
        "Bula" => 6,
        "Cabusao" => 7,
        "Calabanga" => 8,
        "Camaligan" => 9,
        "Canaman" => 10,
        "Caramoan" => 11,
        "Del Gallego" => 12,
        "Gainza" => 13,
        "Garchitorena" => 14,
        "Goa" => 15,
        "Lagonoy" => 16,
        "Libmanan" => 17,
        "<PERSON><PERSON>" => 18,
        "Magarao" => 19,
        "<PERSON><PERSON>or" => 20,
        "Minalabac" => 21,
        "Nabua" => 22,
        "Ocampo" => 23,
        "<PERSON><PERSON>lona" => 24,
        "Pasacao" => 25,
        "<PERSON><PERSON><PERSON><PERSON>" => 26,
        "<PERSON><PERSON>" => 27,
        "<PERSON>ga<PERSON>" => 28,
        "<PERSON><PERSON><PERSON>" => 29,
        "San Fernando" => 30,
        "San Jose" => 31,
        "<PERSON><PERSON>cot" => 32,
        "Siruma" => 33,
        "Tigaon" => 34,
        "Tinambac" => 35
    ];

    $preRegisteredAccounts = [
        "Baao" => "baao",
        "Balatan" => "balatan",
        "Bato" => "bato",
        "Bombon" => "bombon",
        "Buhi" => "buhi",
        "Bula" => "bula",
        "Cabusao" => "cabusao",
        "Calabanga" => "calabanga",
        "Camaligan" => "camaligan",
        "Canaman" => "canaman",
        "Caramoan" => "caramoan",
        "Del Gallego" => "delgallego",
        "Gainza" => "gainza",
        "Garchitorena" => "garchitorena",
        "Goa" => "goa",
        "Lagonoy" => "lagonoy",
        "Libmanan" => "libmanan",
        "Lupi" => "lupi",
        "Magarao" => "magarao",
        "Milaor" => "milaor",
        "Minalabac" => "minalabac",
        "Nabua" => "nabua",
        "Ocampo" => "ocampo",
        "Pamplona" => "pamplona",
        "Pasacao" => "pasacao",
        "Parubcan" => "parubcan",
        "Pili" => "pili",
        "Ragay" => "ragay",
        "Sagnay" => "sagnay",
        "San Fernando" => "sanfernando",
        "San Jose" => "sanjose",
        "Sipocot" => "sipocot",
        "Siruma" => "siruma",
        "Tigaon" => "tigaon",
        "Tinambac" => "tinambac"
    ];
  if (array_key_exists($username, $preRegisteredAccounts) && $preRegisteredAccounts[$username] === $password) {
        $_SESSION['user_id'] = $userMappings[$username]; 
        $_SESSION['voted'] = false; 
        header("Location: vote.html"); 
        exit();
} else {
        $_SESSION['error_message'] = 'Invalid name or password';
        header("Location: login-process-sample.php");
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="login-process-sample.css">
    <title>DFPTA E-Voting System</title>
    <link rel="icon" href="dfptalogo.png" type="image/x-icon">
    <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
<header class="header">
        <div class="container-nav">
            <div class="logo-container">
                <img src="dfptalogo.png" alt="Online Voting System Logo" class="logo">
                <b> DFPTA E-VOTING SYSTEM</b>
            </div>
            <div class="nav-center">
                <a class="nav-button" href="index.html">Home</a>
                <a class="nav-button" href=".html">Admin Login</a>
                <a class="nav-button" href=".php">Contact</a>
            </div>
        </div>
</header>

<body>
<section class="content">
        <div class="container-all">
            <div class="container content-container">
                <div class="login-container">
                    <h2>USER LOGIN</h2>
                    <form action="login.php" method="post">
                <label for="username">Municipality</label>
                <input type="text" name="username" placeholder="Municipality" required><br>

                <label for="password">Password</label>
                <input type="password" name="password" placeholder="Password" required><br>

                 <!-- Display the error message if it exists -->
            <?php if (isset($_SESSION['error_message'])) : ?>
                <p class="ermessage"><?php echo $_SESSION['error_message']; ?></p>
                <?php unset($_SESSION['error_message']); ?> <!-- Remove the error message from the session after displaying it -->
            <?php endif; ?>

                <input type="submit" value="Login">
            </form>

                  </div>
            </div>
        </div>
</section>

         

    <footer class="footer">
        <div class="container-footer">
            &copy; 2024. Social Mobilization and Networking - SDO Camarines Sur. All rights reserved.
        </div>
    </footer>
</body>
</html>