{"name": "dfpta-voting-system", "version": "2.0.0", "description": "DFPTA E-Voting System - MERN Stack Application", "type": "module", "scripts": {"dev": "node scripts/dev.js", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "cd backend && npm test && cd ../frontend && npm test", "lint": "cd backend && npm run lint && cd ../frontend && npm run lint", "lint:fix": "cd backend && npm run lint:fix && cd ../frontend && npm run lint:fix", "format": "cd backend && npm run format && cd ../frontend && npm run format", "clean": "rm -rf backend/node_modules frontend/node_modules node_modules backend/dist frontend/dist", "setup": "npm run install:all && npm run setup:env", "setup:env": "copy backend\\.env.example backend\\.env && copy frontend\\.env.example frontend\\.env && echo ✅ Environment files created. Please edit them with your configuration."}, "keywords": ["voting", "election", "dfpta", "mern", "react", "nodejs", "mongodb", "express"], "author": "DFPTA Development Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["backend", "frontend"], "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "express-async-handler": "^1.2.0", "react-hot-toast": "^2.5.2"}}