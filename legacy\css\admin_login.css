/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body {
    font-family: Arial, sans-serif;
    background-color: #f7f7f7;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin: 0;
}

.container {
    max-width: 400px; /* Set the maximum width as needed */
    margin: 0 auto; /* Center the container */
   /* outline: 2px solid #243328; */
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.header {
    background: #243328;
    color: #fff;
    padding: 0px 0;
    text-align: center;
}

/* Navigation bar styles */
.navigation {
    background: #fff;
   /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  */
    padding: 5px 20px; /* Increase padding for spacing  5h, 20w is for spacing left and right of nav*/
    display: flex;
    justify-content: space-between; /* Align items to the start and end of the container */
    align-items: center;
   outline: 2px solid #243328;
   margin: 0px;
}

.nav-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap; /* Allow items to wrap to the next line */
    gap: 20px;
}

.nav-item {
    font-size: 18px;
    font-weight: bold;
}

.nav-link {
    color: #243328;
}

/* Login section styles */
.login-section {
    background-image: url('bg.jpg');
    background-size: cover; /* Use 'cover' to maintain aspect ratio and cover the entire container */
    background-repeat: no-repeat; /* Prevent the background from repeating */
    background-position: center; /* Center the background image */
    flex: 1;
    padding: 40px 20px; /* Adjust padding for responsiveness */
    text-align: center;
    margin-top: 0;/*40px*/
}

.section-title {
    font-size: 24px;
    color: white;
    margin-top: 20px; /* Adjust the margin-top value as needed */
    margin-bottom: 20px;
}

h2.section-title {
    margin-top: 80px; /* Adjust the margin-top value as needed */
}

/* Form styles */
form {
    margin-top: 20px; /* Add some space between the password input and error message */
    display: flex;
    flex-direction: column;
    align-items: center;
    }

label {
    font-weight: bold;
    font-size: 12px;
    color: white;
}

/* Update the existing input style */
input {
    width: calc(100% - 20px); /* Adjust the width for responsiveness, subtracting padding */
    max-width: 300px; /* Add max-width for responsiveness */
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 5px;
}


input[type="submit"] {
    width: 100%; /* Adjust the width for responsiveness */
    max-width: 300px; /* Add max-width for responsiveness */
    background-color: #35e85f;
    color: #fff;
    font-weight: bold;
    border: none;
    padding: 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

input[type="submit"]:hover {
    background-color: #45a045;
}

/* Footer styles */
.footer {
    background: #fff;
    padding: 10px 5px; /* Adjust padding for responsiveness 20 10*/
    text-align: center;
    margin: 0px;
}
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    max-width: 50px; /* Adjust the width as needed */
    height: auto;
    margin-right: 10px; /* Add some margin for spacing */
}

h1 {
    font-size: 18px; /* Adjust the font size as needed */
    margin: 0; /* Remove default margin */
}

.container-footer {
    font-size: 12px;
}

/* Media Query for Tablets and Smaller Screens */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
    }
    .nav-list {
        flex-direction: column;
        gap: 10px;
    }
    .login-section {
        padding: 40px 10px;
    }
    input[type="submit"] {
        width: 100%;
        max-width: none;
    }
}
/* Add this style for the error message */
.ermessage {
    color: rgb(255, 244, 244);
    margin-top: 1px;
    margin-left: 10px;
    font-style: italic;
    font-size: 14px;
}