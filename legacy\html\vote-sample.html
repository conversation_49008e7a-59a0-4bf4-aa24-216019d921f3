<!DOCTYPE html>
<html>
<head>
    <title>DFPTA E-Voting System</title>
    <link rel="stylesheet" type="text/css" href="vote-sample.css"> <!-- Link to your CSS file -->
    <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@400;700&display=swap" rel="stylesheet">
    <link rel="icon" href="dfptalogo.png" type="image/x-icon"> 
</head>

<header class="header">
    <div class="container-nav">
        <div class="logo-container">
            <img src="dfptalogo.png" alt="Online Voting System Logo" class="logo">
            <a href="index.html"><b>DFPTA E-VOTING SYSTEM</b></a>
        </div>
        <nav class="nav-center">
       <!--     <a class="nav-button" href="contact.php">Contact</a> -->
        </nav>
    </div>
</header>
<body>
    <section class="form-section" style="background-image: url('ff/bgg1.png');">
    <!--<h1>Cast Your Vote</h1> -->
        <div class="form-container">
            <form action="vote.php" method="post">
                <label>CHOOSE YOUR CANDIDATE:</label><br>
                <div class="candidates">
                    <!-- Column 1 -->
                    <div><input type="checkbox" name="candidate_id[]" value="1"> Baao</div>
                    <div><input type="checkbox" name="candidate_id[]" value="8"> Calabanga</div>
                    <div><input type="checkbox" name="candidate_id[]" value="15"> Goa</div>
                    <div><input type="checkbox" name="candidate_id[]" value="22"> Nabua</div>
                    <div><input type="checkbox" name="candidate_id[]" value="29"> Sagnay</div>
                </div>
                <div class="candidates">
                    <!-- Column 2 -->
                    <div><input type="checkbox" name="candidate_id[]" value="2"> Balatan</div>
                    <div><input type="checkbox" name="candidate_id[]" value="9"> Camaligan</div>
                    <div><input type="checkbox" name="candidate_id[]" value="16"> Lagonoy</div>
                    <div><input type="checkbox" name="candidate_id[]" value="23"> Ocampo</div>
                    <div><input type="checkbox" name="candidate_id[]" value="30"> San Fernando</div>
                </div>
                <div class="candidates">
                    <!-- Column 3 -->
                    <div><input type="checkbox" name="candidate_id[]" value="3"> Bato</div>
                    <div><input type="checkbox" name="candidate_id[]" value="10"> Canaman</div>
                    <div><input type="checkbox" name="candidate_id[]" value="17"> Libmanan</div>
                    <div><input type="checkbox" name="candidate_id[]" value="24"> Pamplona</div>
                    <div><input type="checkbox" name="candidate_id[]" value="31"> San Jose</div>
                </div>
                <div class="candidates">
                    <!-- Column 4 -->
                    <div><input type="checkbox" name="candidate_id[]" value="4"> Bombon</div>
                    <div><input type="checkbox" name="candidate_id[]" value="11"> Caramoan</div>
                    <div><input type="checkbox" name="candidate_id[]" value="18"> Lupi</div>
                    <div><input type="checkbox" name="candidate_id[]" value="25"> Pasacao</div>
                    <div><input type="checkbox" name="candidate_id[]" value="32"> Sipocot</div>
                </div>
                <div class="candidates">
                    <!-- Column 5 -->
                    <div><input type="checkbox" name="candidate_id[]" value="5"> Buhi</div>
                    <div><input type="checkbox" name="candidate_id[]" value="12"> Del Gallego</div>
                    <div><input type="checkbox" name="candidate_id[]" value="19"> Magarao</div>
                    <div><input type="checkbox" name="candidate_id[]" value="26"> Parubcan</div>
                    <div><input type="checkbox" name="candidate_id[]" value="33"> Siruma</div>
                </div>
                <div class="candidates">
                    <!-- Column 6 -->
                    <div><input type="checkbox" name="candidate_id[]" value="6"> Bula</div>
                    <div><input type="checkbox" name="candidate_id[]" value="13"> Gainza</div>
                    <div><input type="checkbox" name="candidate_id[]" value="20"> Milaor</div>
                    <div><input type="checkbox" name="candidate_id[]" value="27"> Pili</div>
                    <div><input type="checkbox" name="candidate_id[]" value="34"> Tigaon</div>
                </div>
                <div class="candidates">
                    <!-- Column 7 -->
                    <div><input type="checkbox" name="candidate_id[]" value="7"> Cabusao</div>
                    <div><input type="checkbox" name="candidate_id[]" value="14"> Garchitorena</div>
                    <div><input type="checkbox" name="candidate_id[]" value="21"> Minalabac</div>
                    <div><input type="checkbox" name="candidate_id[]" value="28"> Ragay</div>
                    <div><input type="checkbox" name="candidate_id[]" value="35"> Tinambac</div>
                <!-- Continue adding more columns as needed -->
                </div> 
                <div class="vote-button">
                    <input type="submit" value="Vote">
                </div>
            </form>
        </div>
        <script>
            function validateForm() {
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                if (checkboxes.length > 15) {
                    alert('You can vote for up to 15 candidates only.');
                    return false;
                }
                return true;
            }
    
            function limitCheckboxes() {
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                for (var i = 0; i < checkboxes.length; i++) {
                    checkboxes[i].addEventListener('change', function() {
                        var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                        if (checkedCheckboxes.length > 15) {
                            alert('You can vote for up to 15 candidates only.');
                            this.checked = false;
                        }
                    });
                }
            }
            window.onload = limitCheckboxes;
    </script>
    </section>
</body>
</html>
