import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/hooks/useAuth'
import { useVotingStatus } from '@/hooks/useVoting'
import {
  Calendar,
  CheckCircle,
  Clock,
  History,
  Info,
  LogOut,
  Settings,
  User,
  Vote,
} from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

interface VoterDashboardProps {
  className?: string
  onStartVoting?: () => void
}

export function VoterDashboard({ className, onStartVoting }: VoterDashboardProps) {
  const { user, logout } = useAuth()
  const { data: votingStatus, isLoading } = useVotingStatus()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  const handleLogout = () => {
    logout()
  }

  const getVotingStatusInfo = () => {
    if (!votingStatus)
      return { icon: Clock, color: 'text-gray-500', text: 'Loading...', bgColor: 'bg-gray-100' }

    if (votingStatus.hasVoted) {
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        text: 'Vote Submitted',
        bgColor: 'bg-green-100',
      }
    }

    if (votingStatus.votingStatus === 'active') {
      return {
        icon: Vote,
        color: 'text-blue-600',
        text: 'Voting Active',
        bgColor: 'bg-blue-100',
      }
    }

    return {
      icon: Clock,
      color: 'text-orange-600',
      text: 'Voting Inactive',
      bgColor: 'bg-orange-100',
    }
  }

  const statusInfo = getVotingStatusInfo()
  const StatusIcon = statusInfo.icon

  return (
    <div className={`min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 ${className}`}>
      <div className='mx-auto max-w-6xl p-6'>
        {/* Header */}
        <div className='mb-8 flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Voter Dashboard</h1>
            <p className='mt-1 text-gray-600'>DFPTA E-Voting System</p>
          </div>
          <div className='flex items-center gap-3'>
            <div className='text-right'>
              <p className='text-sm font-medium text-gray-900'>{user?.municipality}</p>
              <Badge variant='secondary' className='text-xs'>
                {user?.role}
              </Badge>
            </div>
            <Button variant='outline' onClick={handleLogout} className='flex items-center gap-2'>
              <LogOut className='h-4 w-4' />
              Logout
            </Button>
          </div>
        </div>

        {/* Enhanced Status Card with Progress */}
        <Card className='border-l-primary mb-8 border-l-4 shadow-lg'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-4'>
                <div className={`rounded-full p-3 ${statusInfo.bgColor} shadow-sm`}>
                  <StatusIcon className={`h-6 w-6 ${statusInfo.color}`} />
                </div>
                <div>
                  <h3 className='text-lg font-semibold'>{statusInfo.text}</h3>
                  <p className='text-gray-600'>
                    {votingStatus?.hasVoted
                      ? 'Thank you for participating in the election'
                      : votingStatus?.votingStatus === 'active'
                        ? 'You can cast your vote now'
                        : 'Voting is currently not available'}
                  </p>
                  {/* Progress indicator */}
                  {votingStatus?.votingStatus === 'active' && (
                    <div className='mt-2 flex items-center gap-2 text-sm text-gray-500'>
                      <div className='flex items-center gap-1'>
                        <div
                          className={`h-2 w-2 rounded-full ${votingStatus?.hasVoted ? 'bg-green-500' : 'bg-gray-300'}`}
                        />
                        <span>Voting {votingStatus?.hasVoted ? 'Complete' : 'Pending'}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {votingStatus?.votingStatus === 'active' && !votingStatus?.hasVoted && (
                <div className='flex flex-col gap-2'>
                  <Button
                    onClick={onStartVoting || (() => navigate('/vote'))}
                    className='bg-primary hover:bg-primary/90 flex items-center gap-2'
                    size='lg'
                  >
                    <Vote className='h-4 w-4' />
                    Cast Your Vote
                  </Button>
                  <p className='text-center text-xs text-gray-500'>
                    Select up to {votingStatus?.maxCandidatesPerVote || 0} candidates
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Dashboard Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-6'>
          <TabsList className='grid w-full grid-cols-4'>
            <TabsTrigger value='overview' className='flex items-center gap-2'>
              <Info className='h-4 w-4' />
              Overview
            </TabsTrigger>
            <TabsTrigger value='voting' className='flex items-center gap-2'>
              <Vote className='h-4 w-4' />
              Voting
            </TabsTrigger>
            <TabsTrigger value='history' className='flex items-center gap-2'>
              <History className='h-4 w-4' />
              History
            </TabsTrigger>
            <TabsTrigger value='profile' className='flex items-center gap-2'>
              <User className='h-4 w-4' />
              Profile
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value='overview' className='space-y-6'>
            <div className='grid gap-6 md:grid-cols-2'>
              {/* Voting Information */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Calendar className='h-5 w-5' />
                    Current Session
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <span className='text-gray-600'>Status:</span>
                    <Badge
                      variant={votingStatus?.votingStatus === 'active' ? 'default' : 'secondary'}
                    >
                      {votingStatus?.votingStatus === 'active' ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-gray-600'>Max Selections:</span>
                    <span className='font-medium'>{votingStatus?.maxCandidatesPerVote || 0}</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-gray-600'>Your Vote:</span>
                    <Badge variant={votingStatus?.hasVoted ? 'default' : 'outline'}>
                      {votingStatus?.hasVoted ? 'Submitted' : 'Pending'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className='space-y-3'>
                  {votingStatus?.votingStatus === 'active' && !votingStatus?.hasVoted && (
                    <Button
                      onClick={onStartVoting || (() => navigate('/vote'))}
                      className='bg-primary hover:bg-primary/90 w-full justify-start text-white'
                    >
                      <Vote className='mr-2 h-4 w-4' />
                      Start Voting Now
                    </Button>
                  )}
                  <Button
                    variant='outline'
                    className='w-full justify-start'
                    onClick={() => navigate('/results')}
                  >
                    <CheckCircle className='mr-2 h-4 w-4' />
                    View Results
                  </Button>
                  <Button
                    variant='outline'
                    className='w-full justify-start'
                    onClick={() => setActiveTab('voting')}
                  >
                    <Info className='mr-2 h-4 w-4' />
                    Voting Instructions
                  </Button>
                  <Button
                    variant='outline'
                    className='w-full justify-start'
                    onClick={() => setActiveTab('profile')}
                  >
                    <Settings className='mr-2 h-4 w-4' />
                    Profile Settings
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Voting Tab */}
          <TabsContent value='voting' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Voting Instructions</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='rounded-lg border border-blue-200 bg-blue-50 p-4'>
                  <h4 className='mb-2 font-semibold text-blue-900'>How to Vote:</h4>
                  <ul className='space-y-2 text-sm text-blue-800'>
                    <li className='flex items-start gap-2'>
                      <span className='font-bold'>1.</span>
                      <span>Click "Cast Your Vote" button when voting is active</span>
                    </li>
                    <li className='flex items-start gap-2'>
                      <span className='font-bold'>2.</span>
                      <span>
                        Select up to {votingStatus?.maxCandidatesPerVote || 0} candidates from the
                        list
                      </span>
                    </li>
                    <li className='flex items-start gap-2'>
                      <span className='font-bold'>3.</span>
                      <span>Use search and filters to find specific candidates</span>
                    </li>
                    <li className='flex items-start gap-2'>
                      <span className='font-bold'>4.</span>
                      <span>Review your selections carefully before submitting</span>
                    </li>
                    <li className='flex items-start gap-2'>
                      <span className='font-bold'>5.</span>
                      <span>Once submitted, votes cannot be changed</span>
                    </li>
                  </ul>
                </div>

                <div className='rounded-lg border border-amber-200 bg-amber-50 p-4'>
                  <h4 className='mb-2 font-semibold text-amber-900'>Important Notes:</h4>
                  <ul className='space-y-1 text-sm text-amber-800'>
                    <li>• Voting is anonymous and secure</li>
                    <li>• You can only vote once</li>
                    <li>• Make sure to submit your vote before the deadline</li>
                    <li>• Contact support if you encounter any issues</li>
                  </ul>
                </div>

                {votingStatus?.votingStatus === 'active' && !votingStatus?.hasVoted && (
                  <div className='pt-4 text-center'>
                    <Button
                      onClick={onStartVoting || (() => navigate('/vote'))}
                      size='lg'
                      className='px-8'
                    >
                      <Vote className='mr-2 h-5 w-5' />
                      Start Voting Now
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value='history' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Voting History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {votingStatus?.hasVoted ? (
                    <div className='flex items-center gap-3 rounded-lg border border-green-200 bg-green-50 p-4'>
                      <CheckCircle className='h-5 w-5 text-green-600' />
                      <div>
                        <p className='font-medium text-green-900'>Vote Submitted</p>
                        <p className='text-sm text-green-700'>
                          You have successfully submitted your vote
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className='py-8 text-center text-gray-500'>
                      <History className='mx-auto mb-4 h-12 w-12 opacity-50' />
                      <p>No voting history available</p>
                      <p className='text-sm'>Your voting activity will appear here</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Profile Tab */}
          <TabsContent value='profile' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid gap-4 md:grid-cols-2'>
                  <div>
                    <label className='text-sm font-medium text-gray-600'>Municipality</label>
                    <p className='text-lg font-semibold'>{user?.municipality}</p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-600'>Role</label>
                    <p className='text-lg font-semibold capitalize'>{user?.role}</p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-600'>Username</label>
                    <p className='text-lg font-semibold'>{user?.username}</p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-600'>Status</label>
                    <Badge variant='default'>Active</Badge>
                  </div>
                </div>

                <div className='mt-6 rounded-lg border bg-gray-50 p-4'>
                  <h4 className='mb-2 font-semibold text-gray-900'>Account Security</h4>
                  <p className='mb-3 text-sm text-gray-600'>
                    Your account is secured with encrypted authentication. Contact your
                    administrator if you need to update your credentials.
                  </p>
                  <Button variant='outline' size='sm'>
                    Contact Support
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
