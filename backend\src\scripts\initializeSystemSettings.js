import mongoose from 'mongoose'
import SystemSettings from '../models/SystemSettings.js'
import logger from '../utils/logger.js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

/**
 * Script to initialize system settings with default values
 */
async function initializeSystemSettings() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Connected to MongoDB for system settings initialization')

    // Initialize default settings
    await SystemSettings.initializeDefaults()
    
    logger.info('✅ System settings initialized successfully')

    // Display current settings
    const settings = await SystemSettings.find().sort({ category: 1, key: 1 })
    
    logger.info('Current system settings:')
    settings.forEach(setting => {
      logger.info(`- ${setting.key}: ${setting.value} (${setting.type}) - ${setting.description}`)
    })

  } catch (error) {
    logger.error('Error initializing system settings:', error)
    throw error
  } finally {
    await mongoose.disconnect()
    logger.info('Disconnected from MongoDB')
  }
}

/**
 * Script to validate system settings
 */
async function validateSystemSettings() {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Validating system settings...')

    const settings = await SystemSettings.find()
    let isValid = true
    
    logger.info(`Found ${settings.length} system settings`)
    
    for (const setting of settings) {
      try {
        // Validate value type
        if (!setting.validateValueType()) {
          logger.error(`❌ Setting ${setting.key}: Invalid value type. Expected ${setting.type}, got ${typeof setting.value}`)
          isValid = false
        } else {
          logger.info(`✅ Setting ${setting.key}: Valid (${setting.type})`)
        }
      } catch (error) {
        logger.error(`❌ Setting ${setting.key}: Validation error - ${error.message}`)
        isValid = false
      }
    }

    // Check for required settings
    const requiredSettings = [
      'public_results_enabled',
      'voting_session_active',
      'max_candidates_per_vote',
      'system_maintenance_mode'
    ]

    for (const requiredKey of requiredSettings) {
      const setting = settings.find(s => s.key === requiredKey)
      if (!setting) {
        logger.error(`❌ Required setting missing: ${requiredKey}`)
        isValid = false
      } else {
        logger.info(`✅ Required setting found: ${requiredKey}`)
      }
    }

    if (isValid) {
      logger.info('✅ All system settings are valid')
    } else {
      logger.error('❌ System settings validation failed')
    }

    return isValid

  } catch (error) {
    logger.error('Error validating system settings:', error)
    return false
  } finally {
    await mongoose.disconnect()
  }
}

/**
 * Script to reset all settings to defaults
 */
async function resetSystemSettings() {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Resetting system settings to defaults...')

    const settings = await SystemSettings.find({ defaultValue: { $exists: true } })
    
    for (const setting of settings) {
      if (setting.defaultValue !== undefined) {
        setting.value = setting.defaultValue
        setting.lastModifiedAt = new Date()
        setting.lastModifiedBy = null
        await setting.save()
        logger.info(`Reset ${setting.key} to default value: ${setting.defaultValue}`)
      }
    }

    logger.info('✅ System settings reset to defaults successfully')

  } catch (error) {
    logger.error('Error resetting system settings:', error)
    throw error
  } finally {
    await mongoose.disconnect()
  }
}

/**
 * Script to display current settings
 */
async function displaySystemSettings() {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Current system settings:')

    const settings = await SystemSettings.find()
      .populate('lastModifiedBy', 'username')
      .sort({ category: 1, key: 1 })
    
    const categories = [...new Set(settings.map(s => s.category))]
    
    for (const category of categories) {
      logger.info(`\n📁 ${category.toUpperCase()} SETTINGS:`)
      const categorySettings = settings.filter(s => s.category === category)
      
      for (const setting of categorySettings) {
        const modifiedBy = setting.lastModifiedBy ? setting.lastModifiedBy.username : 'System'
        const modifiedAt = setting.lastModifiedAt ? setting.lastModifiedAt.toISOString() : 'Never'
        
        logger.info(`  • ${setting.key}: ${setting.value}`)
        logger.info(`    Description: ${setting.description}`)
        logger.info(`    Type: ${setting.type} | Editable: ${setting.isEditable}`)
        logger.info(`    Last modified by: ${modifiedBy} at ${modifiedAt}`)
        logger.info('')
      }
    }

  } catch (error) {
    logger.error('Error displaying system settings:', error)
    throw error
  } finally {
    await mongoose.disconnect()
  }
}

// Run the appropriate script based on command line argument
const command = process.argv[2]

if (command === 'init') {
  initializeSystemSettings()
    .then(() => {
      logger.info('System settings initialization completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('System settings initialization failed:', error)
      process.exit(1)
    })
} else if (command === 'validate') {
  validateSystemSettings()
    .then((isValid) => {
      logger.info('System settings validation completed')
      process.exit(isValid ? 0 : 1)
    })
    .catch((error) => {
      logger.error('System settings validation failed:', error)
      process.exit(1)
    })
} else if (command === 'reset') {
  resetSystemSettings()
    .then(() => {
      logger.info('System settings reset completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('System settings reset failed:', error)
      process.exit(1)
    })
} else if (command === 'display') {
  displaySystemSettings()
    .then(() => {
      logger.info('System settings display completed')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('System settings display failed:', error)
      process.exit(1)
    })
} else {
  console.log('Usage:')
  console.log('  node src/scripts/initializeSystemSettings.js init     - Initialize default settings')
  console.log('  node src/scripts/initializeSystemSettings.js validate - Validate current settings')
  console.log('  node src/scripts/initializeSystemSettings.js reset    - Reset settings to defaults')
  console.log('  node src/scripts/initializeSystemSettings.js display  - Display current settings')
  process.exit(1)
}
