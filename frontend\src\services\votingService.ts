import { apiClient } from './apiClient'

export interface Candidate {
  _id: string
  municipalityName: string
  district: string
  totalVotes: number
  currentRank?: number
  finalPosition?: number
  isWinner: boolean
  isEliminated: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface VotingStatus {
  canVote: boolean
  hasVoted: boolean
  votingStatus:
    | 'no-active-session'
    | 'not-started'
    | 'active'
    | 'completed'
    | 'cancelled'
    | 'paused'
  sessionId?: string
  maxCandidatesPerVote: number
  user: {
    municipality: string
    role: string
    lastVotedAt?: string
  }
  message?: string
}

export interface VoteSubmissionResponse {
  message: string
  votesCount: number
  batchId: string
  submittedAt: string
}

export interface VotingHistory {
  sessionId: string
  sessionName: string
  round: number
  voteType: 'regular' | 'tiebreaker' | 'officer'
  batchId: string
  timestamp: string
  candidates: Array<{
    id: string
    name: string
    municipality: string
  }>
}

export interface VotingResults {
  results: Array<{
    rank: number
    candidateId?: string
    candidateName: string
    municipality: string
    voteCount: number
    percentage: string
  }>
  statistics: {
    totalVotes: number
    uniqueVoters: number
    participatingMunicipalities: number
    participationRate: number
  }
  session: {
    id: string
    name: string
    status: string
    round: number
    maxCandidatesPerVote: number
  }
  participationByMunicipality: Array<{
    municipality: string
    voteCount: number
    candidateCount: number
    firstVote: string
    lastVote: string
  }>
}

export const votingService = {
  // Get all active candidates
  async getCandidates(): Promise<{ candidates: Candidate[]; count: number }> {
    const response = await apiClient.get('/candidates')
    return response.data.data
  },

  // Get voting status for current user
  async getVotingStatus(): Promise<VotingStatus> {
    const response = await apiClient.get('/voting/status')
    return response.data.data
  },

  // Submit votes for selected candidates
  async submitVotes(candidateIds: string[]): Promise<VoteSubmissionResponse> {
    const response = await apiClient.post('/voting/submit', { candidateIds })
    return response.data.data
  },

  // Get public voting results
  async getPublicResults(): Promise<any> {
    const response = await apiClient.get('/results')
    return response.data.data
  },

  // Check if public results are enabled
  async getPublicResultsStatus(): Promise<{ enabled: boolean }> {
    const response = await apiClient.get('/results/status')
    return response.data.data
  },

  // Get user's voting history
  async getVotingHistory(): Promise<{ votingHistory: VotingHistory[]; totalVotes: number }> {
    const response = await apiClient.get('/voting/history')
    return response.data.data
  },

  // Get current voting results (public)
  async getResults(round: number = 1, detailed: boolean = false): Promise<VotingResults> {
    const response = await apiClient.get(`/voting/results?round=${round}&detailed=${detailed}`)
    return response.data.data
  },

  // Tie-breaker methods
  async getActiveTieBreaker(): Promise<any> {
    const response = await apiClient.get('/tiebreaker')
    return response.data.data
  },

  async submitTieBreakerVote(candidateId: string): Promise<any> {
    const response = await apiClient.post('/tiebreaker/vote', { candidateId })
    return response.data.data
  },

  async getTieBreakerResults(tieBreakerID: string): Promise<any> {
    const response = await apiClient.get(`/tiebreaker/results/${tieBreakerID}`)
    return response.data.data
  },

  // Utility functions
  async checkVotingEligibility(): Promise<boolean> {
    try {
      const status = await this.getVotingStatus()
      return status.canVote && !status.hasVoted
    } catch (error) {
      console.error('Error checking voting eligibility:', error)
      return false
    }
  },

  async getRemainingSelections(currentSelections: string[]): Promise<number> {
    try {
      const status = await this.getVotingStatus()
      return Math.max(0, status.maxCandidatesPerVote - currentSelections.length)
    } catch (error) {
      console.error('Error calculating remaining selections:', error)
      return 0
    }
  },

  // Validation helpers
  validateCandidateSelection(
    candidateIds: string[],
    maxSelections: number
  ): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!candidateIds || candidateIds.length === 0) {
      errors.push('Please select at least one candidate')
    }

    if (candidateIds.length > maxSelections) {
      errors.push(`You can select a maximum of ${maxSelections} candidates`)
    }

    // Check for duplicates
    const uniqueIds = [...new Set(candidateIds)]
    if (uniqueIds.length !== candidateIds.length) {
      errors.push('Duplicate candidate selections are not allowed')
    }

    // Validate ObjectId format
    const objectIdRegex = /^[0-9a-fA-F]{24}$/
    const invalidIds = candidateIds.filter(id => !objectIdRegex.test(id))
    if (invalidIds.length > 0) {
      errors.push('Invalid candidate ID format detected')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  },

  // Format helpers
  formatVotingStatus(status: VotingStatus['votingStatus']): string {
    const statusMap = {
      'no-active-session': 'No Active Session',
      'not-started': 'Not Started',
      round1: 'Round 1 - Active',
      tiebreaker: 'Tie-breaker Round',
      completed: 'Completed',
      cancelled: 'Cancelled',
      paused: 'Paused',
    }
    return statusMap[status] || 'Unknown Status'
  },

  formatTimeRemaining(deadline: string): string {
    const now = new Date()
    const deadlineDate = new Date(deadline)
    const diff = deadlineDate.getTime() - now.getTime()

    if (diff <= 0) return 'Expired'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days} day${days !== 1 ? 's' : ''} remaining`
    }

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`
    }

    return `${minutes} minute${minutes !== 1 ? 's' : ''} remaining`
  },

  // Local storage helpers for draft votes
  saveDraftVotes(candidateIds: string[]): void {
    try {
      localStorage.setItem('dfpta_draft_votes', JSON.stringify(candidateIds))
    } catch (error) {
      console.warn('Failed to save draft votes:', error)
    }
  },

  loadDraftVotes(): string[] {
    try {
      const draft = localStorage.getItem('dfpta_draft_votes')
      return draft ? JSON.parse(draft) : []
    } catch (error) {
      console.warn('Failed to load draft votes:', error)
      return []
    }
  },

  clearDraftVotes(): void {
    try {
      localStorage.removeItem('dfpta_draft_votes')
    } catch (error) {
      console.warn('Failed to clear draft votes:', error)
    }
  },
}
