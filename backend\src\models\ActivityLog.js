import mongoose from 'mongoose'

const activityLogSchema = new mongoose.Schema(
  {
    // Activity identification
    activityType: {
      type: String,
      enum: {
        values: [
          'user_login',
          'user_logout',
          'vote_submitted',
          'vote_reset',
          'user_created',
          'user_updated',
          'user_deleted',
          'candidate_created',
          'candidate_updated',
          'candidate_deleted',
          'session_started',
          'session_ended',
          'tiebreaker_started',
          'tiebreaker_completed',
          'results_exported',
          'system_backup',
          'admin_action',
          'security_event',
        ],
        message: 'Invalid activity type'
      },
      required: [true, 'Activity type is required'],
      index: true,
    },
    // User who performed the action
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Performed by user ID is required'],
      index: true,
    },
    // Target user (if action affects another user)
    targetUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      index: true,
    },
    // Activity details
    description: {
      type: String,
      required: [true, 'Activity description is required'],
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    // Additional metadata
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    // Request information
    ipAddress: {
      type: String,
      trim: true,
      index: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    requestMethod: {
      type: String,
      enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    },
    requestUrl: {
      type: String,
      trim: true,
    },
    // Response information
    responseStatus: {
      type: Number,
      min: 100,
      max: 599,
    },
    responseTime: {
      type: Number, // in milliseconds
      min: 0,
    },
    // Security and audit
    severity: {
      type: String,
      enum: {
        values: ['low', 'medium', 'high', 'critical'],
        message: 'Invalid severity level'
      },
      default: 'low',
      index: true,
    },
    isSecurityEvent: {
      type: Boolean,
      default: false,
      index: true,
    },
    // Categorization
    category: {
      type: String,
      enum: {
        values: ['authentication', 'voting', 'administration', 'user_management', 'system', 'security'],
        message: 'Invalid category'
      },
      required: [true, 'Category is required'],
      index: true,
    },
    // Timestamp
    timestamp: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v
        delete ret.userAgent
        return ret
      },
    },
  }
)

// Compound indexes for performance
activityLogSchema.index({ performedBy: 1, timestamp: -1 })
activityLogSchema.index({ activityType: 1, timestamp: -1 })
activityLogSchema.index({ category: 1, timestamp: -1 })
activityLogSchema.index({ severity: 1, timestamp: -1 })
activityLogSchema.index({ isSecurityEvent: 1, timestamp: -1 })
activityLogSchema.index({ ipAddress: 1, timestamp: -1 })

// Static method to log activity
activityLogSchema.statics.logActivity = async function(options) {
  const {
    activityType,
    performedBy,
    targetUser = null,
    description,
    metadata = {},
    ipAddress = null,
    userAgent = null,
    requestMethod = null,
    requestUrl = null,
    responseStatus = null,
    responseTime = null,
    severity = 'low',
    isSecurityEvent = false,
    category,
  } = options

  const activity = new this({
    activityType,
    performedBy,
    targetUser,
    description,
    metadata,
    ipAddress,
    userAgent,
    requestMethod,
    requestUrl,
    responseStatus,
    responseTime,
    severity,
    isSecurityEvent,
    category,
  })

  return activity.save()
}

// Static method to get recent activities
activityLogSchema.statics.getRecentActivities = function(limit = 50, filters = {}) {
  const query = {}
  
  if (filters.activityType) query.activityType = filters.activityType
  if (filters.performedBy) query.performedBy = filters.performedBy
  if (filters.category) query.category = filters.category
  if (filters.severity) query.severity = filters.severity
  if (filters.isSecurityEvent !== undefined) query.isSecurityEvent = filters.isSecurityEvent
  if (filters.startDate || filters.endDate) {
    query.timestamp = {}
    if (filters.startDate) query.timestamp.$gte = new Date(filters.startDate)
    if (filters.endDate) query.timestamp.$lte = new Date(filters.endDate)
  }

  return this.find(query)
    .populate('performedBy', 'username municipality role')
    .populate('targetUser', 'username municipality role')
    .sort({ timestamp: -1 })
    .limit(limit)
}

// Static method to get activity statistics
activityLogSchema.statics.getActivityStats = async function(timeframe = '24h') {
  const now = new Date()
  let startDate

  switch (timeframe) {
    case '1h':
      startDate = new Date(now.getTime() - 60 * 60 * 1000)
      break
    case '24h':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      break
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    default:
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  }

  const stats = await this.aggregate([
    {
      $match: {
        timestamp: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: {
          activityType: '$activityType',
          category: '$category',
        },
        count: { $sum: 1 },
        latestActivity: { $max: '$timestamp' },
      },
    },
    {
      $sort: { count: -1 },
    },
  ])

  const totalActivities = await this.countDocuments({
    timestamp: { $gte: startDate },
  })

  const securityEvents = await this.countDocuments({
    timestamp: { $gte: startDate },
    isSecurityEvent: true,
  })

  return {
    timeframe,
    totalActivities,
    securityEvents,
    activitiesByType: stats,
  }
}

// Static method to get user activity summary
activityLogSchema.statics.getUserActivitySummary = async function(userId, limit = 20) {
  const activities = await this.find({ performedBy: userId })
    .sort({ timestamp: -1 })
    .limit(limit)
    .select('activityType description timestamp category severity')

  const stats = await this.aggregate([
    {
      $match: { performedBy: new mongoose.Types.ObjectId(userId) },
    },
    {
      $group: {
        _id: '$activityType',
        count: { $sum: 1 },
        lastActivity: { $max: '$timestamp' },
      },
    },
    {
      $sort: { count: -1 },
    },
  ])

  return {
    recentActivities: activities,
    activityStats: stats,
  }
}

const ActivityLog = mongoose.model('ActivityLog', activityLogSchema)

export default ActivityLog
