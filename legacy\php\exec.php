<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// Check if the admin is authenticated
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    // Redirect to the admin login page if not authenticated
    header('Location: admin_login.php');
    exit;
}

// Database connection
include 'db_connection.php';

// Truncate the execom_members table to remove all existing data
$conn->query("DELETE FROM execom_members");
$conn->query("ALTER TABLE execom_members AUTO_INCREMENT = 1");

// Check if the top 15 candidates are available in the session
if (!isset($_SESSION['top_15_candidates'])) {
    header('Location: admin_panel.php');
    exit;
}

$top_15_candidates = $_SESSION['top_15_candidates'];

// Insert top 15 candidates and their votes into the execom_members table
$insertedCandidates = 0; // Counter for the number of candidates inserted

foreach ($top_15_candidates as $candidate) {
    if ($insertedCandidates >= 15) {
        // Stop inserting if we have reached the top 15 candidates
        break;
    }

    $candidate_name = $conn->real_escape_string($candidate['candidate_name']);
    $total_votes = $candidate['total_votes'];

    // Get the candidate_id from the candidates table
    $getCandidateIdQuery = "SELECT candidate_id FROM candidates WHERE candidate_name = '$candidate_name'";
    $result = $conn->query($getCandidateIdQuery);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $candidate_id = $row['candidate_id'];

        // SQL query to insert data into execom_members table
        $insertQuery = "INSERT INTO execom_members (candidate_id, candidate_name, total_votes) VALUES ($candidate_id, '$candidate_name', $total_votes)";

        // Execute the query
        $conn->query($insertQuery);

        $insertedCandidates++; // Increment the counter
    }
}

// Close the database connection
$conn->close();

// Redirect to a success page or wherever needed
header('Location: listfinal.php');
exit;
?>
