<?php
include 'db_connection.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Assuming you have a voter_id (you might need to implement authentication)
$voter_id = 1; // Change this based on your authentication mechanism

// Retrieve the selected candidate's name
$sql = "SELECT candidate_name FROM execom_members
        INNER JOIN chairperson ON execom_members.candidate_id = chairperson.candidate_id
        WHERE chairperson.voter_id = $voter_id";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $selectedCandidateName = $row['candidate_name'];
} else {
    $selectedCandidateName = "Unknown Candidate";
}

// Close the database connection
$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Vote Success</title>
    <link rel="stylesheet" type="text/css" href="vote.css">
</head>
<body>
    <h1>Vote Successful</h1>
   
     <!-- Logout Form -->
     <form action="bod_logout.php" method="post">
        <input type="submit" value="Logout">
    </form>
</body>
</html>
