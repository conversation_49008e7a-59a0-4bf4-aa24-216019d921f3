-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1deb5ubuntu1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Nov 16, 2023 at 07:49 AM
-- Server version: 8.0.35-0ubuntu0.22.04.1
-- PHP Version: 8.1.2-1ubuntu2.14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `voting_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `candidates`
--

CREATE TABLE `candidates` (
  `candidate_id` int NOT NULL,
  `candidate_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `candidates`
--

INSERT INTO `candidates` (`candidate_id`, `candidate_name`) VALUES
(1, 'Baao'),
(2, 'Balatan'),
(3, 'Bato'),
(4, 'Bombon'),
(5, 'Buhi'),
(6, 'Bula'),
(7, 'Cabusao'),
(8, 'Calabanga'),
(9, 'Camaligan'),
(10, 'Canaman'),
(11, 'Caramoan'),
(12, 'Del Gallego'),
(13, 'Gainza'),
(14, 'Garchitorena'),
(15, 'Goa'),
(16, 'Lagonoy'),
(17, 'Libmanan'),
(18, 'Lupi'),
(19, 'Magarao'),
(20, 'Milaor'),
(21, 'Minalabac'),
(22, 'Nabua'),
(23, 'Ocampo'),
(24, 'Pamplona'),
(25, 'Pasacao'),
(26, 'Parubcan / Presentacion'),
(27, 'Pili'),
(28, 'Ragay'),
(29, 'Sagnay'),
(30, 'San Fernando'),
(31, 'San Jose'),
(32, 'Sipocot'),
(33, 'Siruma'),
(34, 'Tigaon'),
(35, 'Tinambac');

-- --------------------------------------------------------

--
-- Table structure for table `chairperson`
--

CREATE TABLE `chairperson` (
  `vote_id` int NOT NULL,
  `voter_id` int NOT NULL,
  `candidate_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `execom_members`
--

CREATE TABLE `execom_members` (
  `member_id` int NOT NULL,
  `candidate_id` int DEFAULT NULL,
  `candidate_name` varchar(255) NOT NULL,
  `total_votes` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tie_votes`
--

CREATE TABLE `tie_votes` (
  `vote_id` int NOT NULL,
  `voter_id` int NOT NULL,
  `candidate_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `voters`
--

CREATE TABLE `voters` (
  `voter_id` int NOT NULL,
  `candidate_name` varchar(15) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `voters`
--

INSERT INTO `voters` (`voter_id`, `candidate_name`) VALUES
(1, 'Baao'),
(2, 'Balatan'),
(3, 'Bato'),
(4, 'Bombon'),
(5, 'Buhi'),
(6, 'Bula'),
(7, 'Cabusao'),
(8, 'Calabanga'),
(9, 'Camaligan'),
(10, 'Canaman'),
(11, 'Caramoan'),
(12, 'Del Gallego'),
(13, 'Gainza'),
(14, 'Garchitorena'),
(15, 'Goa'),
(16, 'Lagonoy'),
(17, 'Libmanan'),
(18, 'Lupi'),
(19, 'Magarao'),
(20, 'Milaor'),
(21, 'Minalabac'),
(22, 'Nabua'),
(23, 'Ocampo'),
(24, 'Pamplona'),
(25, 'Pasacao'),
(26, 'Parubcan'),
(27, 'Pili'),
(28, 'Ragay'),
(29, 'Sagnay'),
(30, 'San Fernando'),
(31, 'San Jose'),
(32, 'Sipocot'),
(33, 'Siruma'),
(34, 'Tigaon'),
(35, 'Tinambac');

-- --------------------------------------------------------

--
-- Table structure for table `votes`
--

CREATE TABLE `votes` (
  `vote_id` int NOT NULL,
  `voter_id` int DEFAULT NULL,
  `candidate_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `votes`
--

INSERT INTO `votes` (`vote_id`, `voter_id`, `candidate_id`) VALUES
(1, 12, 8),
(2, 12, 9),
(3, 12, 30),
(4, 12, 17),
(5, 12, 24),
(6, 12, 18),
(7, 12, 25),
(8, 12, 32),
(9, 12, 12),
(10, 12, 33),
(11, 12, 13),
(12, 12, 20),
(13, 12, 7),
(14, 12, 21),
(15, 12, 28),
(16, 14, 1),
(17, 14, 8),
(18, 14, 15),
(19, 14, 9),
(20, 14, 5),
(21, 14, 19),
(22, 14, 33),
(23, 14, 13),
(24, 14, 27),
(25, 14, 14),
(26, 14, 35),
(27, 27, 8),
(28, 27, 22),
(29, 27, 2),
(30, 27, 23),
(31, 27, 30),
(32, 27, 3),
(33, 27, 17),
(34, 27, 24),
(35, 27, 18),
(36, 27, 25),
(37, 27, 32),
(38, 27, 33),
(39, 27, 13),
(40, 27, 27),
(41, 27, 28),
(42, 35, 1),
(43, 35, 15),
(44, 35, 22),
(45, 35, 9),
(46, 35, 16),
(47, 35, 23),
(48, 35, 24),
(49, 35, 18),
(50, 35, 5),
(51, 35, 12),
(52, 35, 26),
(53, 35, 33),
(54, 35, 14),
(55, 35, 28),
(56, 35, 35),
(57, 25, 8),
(58, 25, 2),
(59, 25, 9),
(60, 25, 30),
(61, 25, 17),
(62, 25, 18),
(63, 25, 25),
(64, 25, 5),
(65, 25, 12),
(66, 25, 33),
(67, 25, 13),
(68, 25, 27),
(69, 25, 14),
(70, 25, 28),
(71, 25, 35),
(72, 33, 1),
(73, 33, 15),
(74, 33, 22),
(75, 33, 9),
(76, 33, 16),
(77, 33, 24),
(78, 33, 18),
(79, 33, 5),
(80, 33, 12),
(81, 33, 26),
(82, 33, 33),
(83, 33, 14),
(84, 33, 28),
(85, 33, 35),
(86, 9, 1),
(87, 9, 15),
(88, 9, 22),
(89, 9, 9),
(90, 9, 16),
(91, 9, 30),
(92, 9, 17),
(93, 9, 24),
(94, 9, 5),
(95, 9, 35),
(96, 17, 17),
(97, 17, 24),
(98, 17, 25),
(99, 17, 32),
(100, 3, 1),
(101, 3, 22),
(102, 3, 2),
(103, 3, 3),
(104, 3, 5),
(105, 16, 1),
(106, 16, 15),
(107, 16, 22),
(108, 16, 9),
(109, 16, 16),
(110, 16, 23),
(111, 16, 24),
(112, 16, 18),
(113, 16, 5),
(114, 16, 12),
(115, 16, 26),
(116, 16, 33),
(117, 16, 13),
(118, 16, 14),
(119, 16, 35),
(120, 15, 1),
(121, 15, 15),
(122, 15, 22),
(123, 15, 9),
(124, 15, 16),
(125, 15, 23),
(126, 15, 10),
(127, 15, 17),
(128, 15, 24),
(129, 15, 31),
(130, 15, 18),
(131, 15, 32),
(132, 15, 5),
(133, 15, 13),
(134, 15, 35),
(135, 19, 8),
(136, 19, 15),
(137, 19, 22),
(138, 19, 2),
(139, 19, 16),
(140, 19, 24),
(141, 19, 25),
(142, 19, 19),
(143, 19, 13),
(144, 19, 34),
(145, 19, 28),
(146, 19, 35),
(147, 30, 8),
(148, 30, 9),
(149, 30, 30),
(150, 30, 17),
(151, 30, 25),
(152, 30, 32),
(153, 30, 12),
(154, 30, 19),
(155, 30, 13),
(156, 2, 1),
(157, 2, 22),
(158, 2, 2),
(159, 2, 3),
(160, 2, 5),
(161, 26, 1),
(162, 26, 15),
(163, 26, 16),
(164, 26, 31),
(165, 26, 11),
(166, 26, 12),
(167, 26, 26),
(168, 26, 27),
(169, 26, 34),
(170, 26, 14),
(171, 8, 8),
(172, 8, 30),
(173, 8, 27),
(174, 18, 1),
(175, 18, 15),
(176, 18, 22),
(177, 18, 9),
(178, 18, 16),
(179, 18, 23),
(180, 18, 10),
(181, 18, 17),
(182, 18, 24),
(183, 18, 18),
(184, 18, 32),
(185, 18, 5),
(186, 18, 12),
(187, 18, 33),
(188, 18, 35),
(189, 23, 1),
(190, 23, 15),
(191, 23, 22),
(192, 23, 9),
(193, 23, 16),
(194, 23, 23),
(195, 23, 24),
(196, 23, 18),
(197, 23, 5),
(198, 23, 12),
(199, 23, 26),
(200, 23, 33),
(201, 23, 14),
(202, 23, 28),
(203, 23, 35),
(204, 1, 1),
(205, 1, 15),
(206, 1, 22),
(207, 1, 2),
(208, 1, 9),
(209, 1, 16),
(210, 1, 23),
(211, 1, 3),
(212, 1, 10),
(213, 1, 17),
(214, 1, 24),
(215, 1, 32),
(216, 1, 5),
(217, 1, 35),
(218, 13, 1),
(219, 13, 8),
(220, 13, 22),
(221, 13, 9),
(222, 13, 23),
(223, 13, 30),
(224, 13, 17),
(225, 13, 24),
(226, 13, 18),
(227, 13, 25),
(228, 13, 19),
(229, 13, 13),
(230, 13, 27),
(231, 13, 28),
(232, 13, 35),
(233, 5, 1),
(234, 5, 15),
(235, 5, 22),
(236, 5, 9),
(237, 5, 16),
(238, 5, 23),
(239, 5, 24),
(240, 5, 18),
(241, 5, 5),
(242, 5, 12),
(243, 5, 26),
(244, 5, 33),
(245, 5, 14),
(246, 5, 28),
(247, 5, 35),
(248, 28, 1),
(249, 28, 8),
(250, 28, 22),
(251, 28, 2),
(252, 28, 10),
(253, 28, 24),
(254, 28, 18),
(255, 28, 32),
(256, 28, 12),
(257, 28, 19),
(258, 28, 13),
(259, 28, 20),
(260, 28, 34),
(261, 28, 14),
(262, 28, 28),
(263, 24, 1),
(264, 24, 15),
(265, 24, 9),
(266, 24, 17),
(267, 24, 24),
(268, 24, 18),
(269, 24, 25),
(270, 24, 5),
(271, 24, 35),
(272, 32, 1),
(273, 32, 15),
(274, 32, 22),
(275, 32, 9),
(276, 32, 16),
(277, 32, 23),
(278, 32, 30),
(279, 32, 10),
(280, 32, 17),
(281, 32, 24),
(282, 32, 31),
(283, 32, 18),
(284, 32, 32),
(285, 32, 13),
(286, 32, 28),
(287, 22, 1),
(288, 22, 22),
(289, 22, 2),
(290, 22, 3),
(291, 22, 24),
(292, 22, 5);

-- --------------------------------------------------------

--
-- Table structure for table `vote_result`
--

CREATE TABLE `vote_result` (
  `no` int NOT NULL,
  `candidate_name` varchar(255) DEFAULT NULL,
  `candidate_id` int DEFAULT NULL,
  `total_votes` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `candidates`
--
ALTER TABLE `candidates`
  ADD PRIMARY KEY (`candidate_id`);

--
-- Indexes for table `chairperson`
--
ALTER TABLE `chairperson`
  ADD PRIMARY KEY (`vote_id`),
  ADD KEY `voter_id` (`voter_id`),
  ADD KEY `candidate_id` (`candidate_id`);

--
-- Indexes for table `execom_members`
--
ALTER TABLE `execom_members`
  ADD PRIMARY KEY (`member_id`),
  ADD KEY `candidate_id` (`candidate_id`);

--
-- Indexes for table `tie_votes`
--
ALTER TABLE `tie_votes`
  ADD PRIMARY KEY (`vote_id`),
  ADD KEY `fk_voter` (`voter_id`),
  ADD KEY `fk_candidate` (`candidate_id`);

--
-- Indexes for table `voters`
--
ALTER TABLE `voters`
  ADD PRIMARY KEY (`voter_id`);

--
-- Indexes for table `votes`
--
ALTER TABLE `votes`
  ADD PRIMARY KEY (`vote_id`),
  ADD KEY `voter_id` (`voter_id`),
  ADD KEY `candidate_id` (`candidate_id`);

--
-- Indexes for table `vote_result`
--
ALTER TABLE `vote_result`
  ADD PRIMARY KEY (`no`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `candidates`
--
ALTER TABLE `candidates`
  MODIFY `candidate_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=70;

--
-- AUTO_INCREMENT for table `chairperson`
--
ALTER TABLE `chairperson`
  MODIFY `vote_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- AUTO_INCREMENT for table `execom_members`
--
ALTER TABLE `execom_members`
  MODIFY `member_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `tie_votes`
--
ALTER TABLE `tie_votes`
  MODIFY `vote_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `votes`
--
ALTER TABLE `votes`
  MODIFY `vote_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=293;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `chairperson`
--
ALTER TABLE `chairperson`
  ADD CONSTRAINT `chairperson_ibfk_2` FOREIGN KEY (`candidate_id`) REFERENCES `execom_members` (`candidate_id`);

--
-- Constraints for table `execom_members`
--
ALTER TABLE `execom_members`
  ADD CONSTRAINT `execom_members_ibfk_1` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`candidate_id`);

--
-- Constraints for table `tie_votes`
--
ALTER TABLE `tie_votes`
  ADD CONSTRAINT `fk_candidate` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`candidate_id`),
  ADD CONSTRAINT `fk_voter` FOREIGN KEY (`voter_id`) REFERENCES `voters` (`voter_id`);

--
-- Constraints for table `votes`
--
ALTER TABLE `votes`
  ADD CONSTRAINT `votes_ibfk_1` FOREIGN KEY (`voter_id`) REFERENCES `candidates` (`candidate_id`),
  ADD CONSTRAINT `votes_ibfk_2` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`candidate_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
