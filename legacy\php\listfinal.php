<?php
session_start();

// Check if the admin is authenticated
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    // Redirect to the admin login page if not authenticated
    header('Location: admin_login.php');
    exit;
}

// Database connection
include 'db_connection.php';

// SQL query to retrieve all vote results
$sql_all_candidates = "SELECT c.candidate_name, COUNT(v.vote_id) AS total_votes
                      FROM candidates c
                      LEFT JOIN votes v ON c.candidate_id = v.candidate_id
                      GROUP BY c.candidate_name
                      ORDER BY total_votes DESC, c.candidate_name ASC";

$result_all_candidates = $conn->query($sql_all_candidates);

// The top 35 candidates
$top_35_candidates = [];
if ($result_all_candidates->num_rows > 0) {
    $count = 1; // Initialize count
    while ($row = $result_all_candidates->fetch_assoc()) {
        $top_35_candidates[] = $row; // Corrected to include the entire row
        $count++;
    }
}

// Get tied candidates from the session
if (!isset($_SESSION['top_15_candidates'])) {
    header('Location: admin_panel.php');
    exit;
}

$candidates = $_SESSION['top_15_candidates'];

usort($candidates, function ($a, $b) {
    return $b['total_votes'] - $a['total_votes'];
});

$minVotes15 = $candidates[14]['total_votes'];
$tieCandidates15 = array_filter($candidates, function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
});

$tiedCandidates = count($tieCandidates15) > 1 ? array_values($tieCandidates15) : [];

// Generate a comma-separated list of tied candidate names for the SQL query
$tiedCandidateNames = implode("', '", array_column($tiedCandidates, 'candidate_name'));

// SQL query to retrieve vote results only for tied candidates
$sql_tied_candidates = "SELECT c.candidate_name, COUNT(tv.vote_id) AS total_votes
                       FROM candidates c
                       LEFT JOIN tie_votes tv ON c.candidate_id = tv.candidate_id
                       WHERE c.candidate_name IN ('$tiedCandidateNames')
                       GROUP BY c.candidate_name
                       ORDER BY total_votes DESC, c.candidate_name ASC";

$result_tied_candidates = $conn->query($sql_tied_candidates);

// The tied candidates
$tied_candidates = [];
if ($result_tied_candidates->num_rows > 0) {
    while ($row = $result_tied_candidates->fetch_assoc()) {
        $tied_candidates[] = $row; // Corrected to include the entire row
    }
}

// Close the database connection
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Summary - Online Voting System</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: Arial, sans-serif;
            margin: 0;
        }

        header, nav, section, footer {
            text-align: center;
            width: 100%;
        }

        nav ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        nav ul li {
            display: inline;
            margin-right: 10px;
        }

        section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 20px;
            width: 100%;
        }

        .table-container {
            width: 48%; /* Adjust the width as needed */
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        footer {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <header>
        <h1>Summary - Online Voting System</h1>
    </header>

    <nav>
        <ul>
            <li><a href="admin_logout.php">Logout</a></li>
            <li><a href="admin_panel.php">Admin Panel</a></li>
            <li><a href="board_members.php">DFPTA Board Members</a></li>
            <li><a href="exec.php">List</a></li>
        </ul>
    </nav>

    <section>
        <div class="table-container">
            <h3>35 Candidates</h3>
            <table>
                <tr>
                    <th>No</th>
                    <th>Candidate Name</th>
                    <th>Total Votes</th>
                </tr>
                <?php
                if (!empty($top_35_candidates)) {
                    $count = 1;
                    foreach ($top_35_candidates as $row) {
                        echo "<tr>";
                        echo "<td>$count</td>";
                        echo "<td>" . $row["candidate_name"] . "</td>";
                        echo "<td>" . $row["total_votes"] . "</td>";
                        echo "</tr>";
                        $count++;
                    }
                } else {
                    echo "No results available yet.";
                }
                ?>
            </table>
        </div>

        <div class="table-container">
            <h3>Tied Candidates</h3>
            <table>
                <tr>
                    <th>No</th>
                    <th>Candidate Name</th>
                    <th>Total Votes</th>
                </tr>
                <?php
                if (!empty($tied_candidates)) {
                    $count = 1;
                    foreach ($tied_candidates as $row) {
                        echo "<tr>";
                        echo "<td>$count</td>";
                        echo "<td>" . $row["candidate_name"] . "</td>";
                        echo "<td>" . $row["total_votes"] . "</td>";
                        echo "</tr>";
                        $count++;
                    }
                } else {
                    echo "No tied candidates.";
                }
                ?>
            </table>
        </div>
    </section>

    <footer>
        <p>&copy; SOCMOB</p>
    </footer>
</body>
</html>
