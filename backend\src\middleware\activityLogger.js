import ActivityLog from '../models/ActivityLog.js'
import logger from '../utils/logger.js'

// Activity logging middleware
export const activityLogger = (activityType, category, description, options = {}) => {
  return async (req, res, next) => {
    const startTime = Date.now()
    
    // Store original res.json to capture response
    const originalJson = res.json
    
    res.json = function(data) {
      const responseTime = Date.now() - startTime
      
      // Log activity asynchronously to avoid blocking response
      setImmediate(async () => {
        try {
          const logOptions = {
            activityType,
            performedBy: req.user?.id,
            targetUser: options.getTargetUser ? options.getTargetUser(req) : null,
            description: typeof description === 'function' ? description(req, data) : description,
            metadata: {
              ...options.metadata,
              ...(options.getMetadata ? options.getMetadata(req, data) : {}),
            },
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            requestMethod: req.method,
            requestUrl: req.originalUrl,
            responseStatus: res.statusCode,
            responseTime,
            severity: options.severity || 'low',
            isSecurityEvent: options.isSecurityEvent || false,
            category,
          }
          
          await ActivityLog.logActivity(logOptions)
        } catch (error) {
          logger.error('Failed to log activity:', error)
        }
      })
      
      // Call original json method
      return originalJson.call(this, data)
    }
    
    next()
  }
}

// Specific activity loggers
export const logUserLogin = activityLogger(
  'user_login',
  'authentication',
  (req) => `User ${req.user?.username} logged in from ${req.user?.municipality}`,
  {
    severity: 'low',
    getMetadata: (req) => ({
      municipality: req.user?.municipality,
      role: req.user?.role,
    }),
  }
)

export const logUserLogout = activityLogger(
  'user_logout',
  'authentication',
  (req) => `User ${req.user?.username} logged out`,
  {
    severity: 'low',
    getMetadata: (req) => ({
      municipality: req.user?.municipality,
      role: req.user?.role,
    }),
  }
)

export const logVoteSubmission = activityLogger(
  'vote_submitted',
  'voting',
  (req, data) => `User from ${req.user?.municipality} submitted ${req.body?.candidateIds?.length || 0} votes`,
  {
    severity: 'medium',
    getMetadata: (req, data) => ({
      candidateCount: req.body?.candidateIds?.length || 0,
      voterMunicipality: req.user?.municipality,
      batchId: data?.data?.batchId,
      round: data?.data?.round,
    }),
  }
)

export const logVoteReset = activityLogger(
  'vote_reset',
  'administration',
  (req) => `Admin ${req.user?.username} reset votes for user`,
  {
    severity: 'high',
    getTargetUser: (req) => req.params.id,
    getMetadata: (req) => ({
      adminMunicipality: req.user?.municipality,
      resetReason: req.body?.reason || 'No reason provided',
    }),
  }
)

export const logUserCreation = activityLogger(
  'user_created',
  'user_management',
  (req, data) => `Admin ${req.user?.username} created user for ${req.body?.municipality}`,
  {
    severity: 'medium',
    getMetadata: (req, data) => ({
      newUserMunicipality: req.body?.municipality,
      newUserRole: req.body?.role || 'voter',
      adminMunicipality: req.user?.municipality,
    }),
  }
)

export const logUserUpdate = activityLogger(
  'user_updated',
  'user_management',
  (req) => `Admin ${req.user?.username} updated user`,
  {
    severity: 'medium',
    getTargetUser: (req) => req.params.id,
    getMetadata: (req) => ({
      updatedFields: Object.keys(req.body),
      adminMunicipality: req.user?.municipality,
    }),
  }
)

export const logUserDeletion = activityLogger(
  'user_deleted',
  'user_management',
  (req) => `Admin ${req.user?.username} deleted user`,
  {
    severity: 'high',
    getTargetUser: (req) => req.params.id,
    getMetadata: (req) => ({
      adminMunicipality: req.user?.municipality,
      deletionReason: req.body?.reason || 'No reason provided',
    }),
  }
)

export const logResultsExport = activityLogger(
  'results_exported',
  'administration',
  (req) => `Admin ${req.user?.username} exported voting results`,
  {
    severity: 'medium',
    getMetadata: (req) => ({
      exportFormat: req.query?.format || 'csv',
      adminMunicipality: req.user?.municipality,
    }),
  }
)

export const logSecurityEvent = activityLogger(
  'security_event',
  'security',
  (req, description) => description || 'Security event detected',
  {
    severity: 'high',
    isSecurityEvent: true,
  }
)

export const logAdminAction = activityLogger(
  'admin_action',
  'administration',
  (req, description) => description || `Admin ${req.user?.username} performed action`,
  {
    severity: 'medium',
    getMetadata: (req) => ({
      adminMunicipality: req.user?.municipality,
      adminRole: req.user?.role,
    }),
  }
)

// Helper function to manually log activities
export const manualActivityLog = async (options) => {
  try {
    await ActivityLog.logActivity(options)
  } catch (error) {
    logger.error('Failed to manually log activity:', error)
  }
}

// Middleware to log failed authentication attempts
export const logFailedAuth = async (req, res, next) => {
  const originalJson = res.json
  
  res.json = function(data) {
    if (res.statusCode === 401 || res.statusCode === 403) {
      setImmediate(async () => {
        try {
          await ActivityLog.logActivity({
            activityType: 'security_event',
            performedBy: null, // No authenticated user for failed auth
            description: `Failed authentication attempt from ${req.ip}`,
            metadata: {
              attemptedUsername: req.body?.username,
              failureReason: data?.error || 'Authentication failed',
            },
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            requestMethod: req.method,
            requestUrl: req.originalUrl,
            responseStatus: res.statusCode,
            severity: 'high',
            isSecurityEvent: true,
            category: 'security',
          })
        } catch (error) {
          logger.error('Failed to log failed auth attempt:', error)
        }
      })
    }
    
    return originalJson.call(this, data)
  }
  
  next()
}
