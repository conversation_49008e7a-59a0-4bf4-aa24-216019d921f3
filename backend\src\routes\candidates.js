import express from 'express';

const router = express.Router();

/**
 * @swagger
 * /api/candidates:
 *   get:
 *     summary: Get all candidates
 *     tags: [Candidates]
 *     responses:
 *       200:
 *         description: List of candidates retrieved successfully
 */
router.get('/', (req, res) => {
  res.json({ message: 'Get candidates endpoint - to be implemented' });
});

export default router;
