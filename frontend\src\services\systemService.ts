import { apiClient } from './apiClient'

export interface DistrictMunicipalityData {
  districts: string[]
  municipalities: string[]
  districtMapping: Record<string, string[]>
  totalDistricts: number
  totalMunicipalities: number
}

export interface SystemConfig {
  system: {
    name: string
    version: string
    description: string
  }
  statistics: {
    totalCandidates: number
    totalUsers: number
    totalDistricts: number
  }
  configuration: {
    availableRoles: string[]
    maxCandidatesPerVote: number
    votingRounds: string[]
  }
}

export interface MunicipalityDetails {
  municipalityName: string
  district: string
  totalVotes: number
  currentRank?: number
  isActive: boolean
  userCount: number
  lastUpdated: string
}

class SystemService {
  /**
   * Get all districts and municipalities dynamically from the database
   */
  async getDistrictsAndMunicipalities(): Promise<DistrictMunicipalityData> {
    const response = await apiClient.get('/system/districts-municipalities')
    return response.data.data
  }

  /**
   * Get system configuration and metadata
   */
  async getSystemConfig(): Promise<SystemConfig> {
    const response = await apiClient.get('/system/config')
    return response.data.data
  }

  /**
   * Get municipality details by name
   */
  async getMunicipalityDetails(name: string): Promise<MunicipalityDetails> {
    const response = await apiClient.get(`/system/municipality/${encodeURIComponent(name)}`)
    return response.data.data
  }

  /**
   * Helper function to get district for municipality
   */
  async getDistrictForMunicipality(municipalityName: string): Promise<string> {
    try {
      const data = await this.getDistrictsAndMunicipalities()
      
      for (const [district, municipalities] of Object.entries(data.districtMapping)) {
        if (municipalities.includes(municipalityName)) {
          return district
        }
      }
      
      throw new Error(`Municipality ${municipalityName} not found in any district`)
    } catch (error) {
      console.error('Error getting district for municipality:', error)
      throw error
    }
  }

  /**
   * Helper function to get municipalities in district
   */
  async getMunicipalitiesInDistrict(district: string): Promise<string[]> {
    try {
      const data = await this.getDistrictsAndMunicipalities()
      return data.districtMapping[district] || []
    } catch (error) {
      console.error('Error getting municipalities in district:', error)
      throw error
    }
  }

  /**
   * Validate if a municipality exists
   */
  async validateMunicipality(municipalityName: string): Promise<boolean> {
    try {
      const data = await this.getDistrictsAndMunicipalities()
      return data.municipalities.includes(municipalityName)
    } catch (error) {
      console.error('Error validating municipality:', error)
      return false
    }
  }

  /**
   * Validate if a district exists
   */
  async validateDistrict(district: string): Promise<boolean> {
    try {
      const data = await this.getDistrictsAndMunicipalities()
      return data.districts.includes(district)
    } catch (error) {
      console.error('Error validating district:', error)
      return false
    }
  }

  /**
   * Get all available roles from system config
   */
  async getAvailableRoles(): Promise<string[]> {
    try {
      const config = await this.getSystemConfig()
      return config.configuration.availableRoles
    } catch (error) {
      console.error('Error getting available roles:', error)
      return ['voter', 'admin', 'execom', 'tie-breaker'] // fallback
    }
  }

  /**
   * Get max candidates per vote from system config
   */
  async getMaxCandidatesPerVote(): Promise<number> {
    try {
      const config = await this.getSystemConfig()
      return config.configuration.maxCandidatesPerVote
    } catch (error) {
      console.error('Error getting max candidates per vote:', error)
      return 15 // fallback
    }
  }
}

export const systemService = new SystemService()
