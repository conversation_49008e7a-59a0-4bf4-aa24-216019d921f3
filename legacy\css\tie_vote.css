/* Reset default margin and padding for better consistency */
body, h1, h2, p, ul {
    margin: 0;
    padding: 0;
}

/* Basic styling for the header */
header {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 1em 0;
}

/* Style the section containing the form */
section {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* Style the candidate list */
.candidates {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

/* Style each candidate item */
.candidates div {
    flex: 1 0 calc(33.333% - 10px);
    box-sizing: border-box;
}

/* Style the radio button or checkbox */
input[type="radio"], input[type="checkbox"] {
    margin-right: 5px;
}

/* Style the submit button */
button {
    background-color: #4caf50;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
}

/* Add hover effect for the submit button */
button:hover {
    background-color: #45a049;
}

/* Style the footer */
footer {
    margin-top: 20px;
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 10px 0;
}
