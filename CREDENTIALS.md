# DFPTA E-Voting System - Login Credentials
*Updated: 2025-07-28 - Complete Database Reset*

## 🔐 System Access Information

This document contains all login credentials for the DFPTA E-Voting System. The system uses a municipality-based voting approach where each municipality in Camarines Sur has a dedicated user account.

**🔒 SECURITY NOTE**: All passwords are securely hashed using bcrypt (12 rounds) and stored in MongoDB. This document contains the plain text passwords for initial login only.

## 🏛️ Admin Access

### System Administrator
- **Username**: `admin`
- **Password**: `socmob123`
- **Municipality**: Pili
- **District**: 3rd District
- **Role**: Administrator
- **Access**: Full system administration, user management, results viewing

## 🗳️ Municipality Voter Accounts

All municipality passwords follow the secure format: **4 letters + 1 symbol + 3 numbers**
Generated using cryptographically secure random values.

### 1st Congressional District (5 Municipalities)

| Municipality | Username     | District     | Password      |
| ------------ | ------------ | ------------ | ------------- |
| Cabusao      | `cabusao`    | 1st District | `cabu=538`    |
| Del Gallego  | `delgallego` | 1st District | `del #230`    |
| Lupi         | `lupi`       | 1st District | `lupi&171`    |
| Ragay        | `ragay`      | 1st District | `raga*649`    |
| Sipocot      | `sipocot`    | 1st District | `sipo@508`    |

### 2nd Congressional District (7 Municipalities)

| Municipality | Username      | District     | Password      |
| ------------ | ------------- | ------------ | ------------- |
| Gainza       | `gainza`      | 2nd District | `gain#464`    |
| Libmanan     | `libmanan`    | 2nd District | `libm%957`    |
| Milaor       | `milaor`      | 2nd District | `mila#385`    |
| Minalabac    | `minalabac`   | 2nd District | `mina&583`    |
| Pamplona     | `pamplona`    | 2nd District | `pamp$409`    |
| Pasacao      | `pasacao`     | 2nd District | `pasa*841`    |
| San Fernando | `sanfernando` | 2nd District | `san +279`    |

### 3rd Congressional District (7 Municipalities)

| Municipality | Username      | District     | Password      |
| ------------ | ------------- | ------------ | ------------- |
| Bombon       | `bombon`      | 3rd District | `bomb=387`    |
| Calabanga    | `calabanga`   | 3rd District | `cala@618`    |
| Camaligan    | `camaligan`   | 3rd District | `cama=886`    |
| Canaman      | `canaman`     | 3rd District | `cana*744`    |
| Magarao      | `magarao`     | 3rd District | `maga#295`    |
| Ocampo       | `ocampo`      | 3rd District | `ocam#158`    |
| Pili         | `pili`        | 3rd District | `pili#519`    |

### 4th Congressional District (10 Municipalities)

| Municipality   | Username        | District     | Password      |
| -------------- | --------------- | ------------ | ------------- |
| Caramoan       | `caramoan`      | 4th District | `cara+819`    |
| Garchitorena   | `garchitorena`  | 4th District | `garc#461`    |
| Goa            | `goa`           | 4th District | `goax&682`    |
| Lagonoy        | `lagonoy`       | 4th District | `lago+708`    |
| Parubcan       | `parubcan`      | 4th District | `paru$046`    |
| Sagnay         | `sagnay`        | 4th District | `sagn*399`    |
| San Jose       | `sanjose`       | 4th District | `san &172`    |
| Siruma         | `siruma`        | 4th District | `siru&835`    |
| Tigaon         | `tigaon`        | 4th District | `tiga+813`    |
| Tinambac       | `tinambac`      | 4th District | `tina#882`    |

### 5th Congressional District (6 Municipalities)

| Municipality | Username    | District     | Password      |
| ------------ | ----------- | ------------ | ------------- |
| Baao         | `baao`      | 5th District | `baao@414`    |
| Balatan      | `balatan`   | 5th District | `bala#767`    |
| Bato         | `bato`      | 5th District | `bato#471`    |
| Buhi         | `buhi`      | 5th District | `buhi$098`    |
| Bula         | `bula`      | 5th District | `bula$845`    |
| Nabua        | `nabua`     | 5th District | `nabu+794`    |

## 👥 Executive Committee Access

### ExeCom Members

| Username      | Municipality | District     | Password      | Role    |
| ------------- | ------------ | ------------ | ------------- | ------- |
| `execom_pili` | Pili         | 3rd District | `pili#123`    | ExeCom  |
| `execom_nabua`| Nabua        | 5th District | `nabu@456`    | ExeCom  |

## ⚖️ Tie-Breaker Access

### Tie-Breaker Official

| Username     | Municipality | District     | Password      | Role        |
| ------------ | ------------ | ------------ | ------------- | ----------- |
| `tiebreaker` | Pili         | 3rd District | `pili$789`    | Tie-Breaker |

## 🔑 Login Instructions

### For Municipality Representatives:
1. Navigate to: `http://localhost:3000/login`
2. Enter your municipality username (lowercase, no spaces)
3. Enter your generated secure password
4. Click "Login" to access the voting interface

### For Administrators:
1. Navigate to: `http://localhost:3000/login`
2. Enter username: `admin`
3. Enter password: `socmob123`
4. Click "Login" to access the admin dashboard

## 🔒 Security Information

### Password Format
- **Length**: 8 characters
- **Structure**: 4 letters + 1 symbol + 3 numbers
- **Symbols Used**: `#`, `@`, `$`, `%`, `&`, `*`, `+`, `=`
- **Examples**: `cabu=538`, `pili#519`, `nabu+794`

### Security Features
- All passwords are hashed using bcrypt (12 rounds)
- No passwords are stored in plain text
- JWT tokens used for session management
- Automatic logout after vote submission
- CORS protection enabled
- Input sanitization implemented

## 📊 System Statistics

- **Total Users**: 39
- **Municipality Voters**: 35
- **Admin Users**: 1
- **ExeCom Users**: 2
- **Tie-Breaker Users**: 1
- **Total Candidates**: 35 (one per municipality)
- **Congressional Districts**: 5

## 🚨 Important Notes

1. **Data Privacy**: This system stores NO personal information. Only municipality names and districts are used.
2. **One Vote Per Municipality**: Each municipality can cast one vote representing their constituency.
3. **Vote Limit**: Maximum 15 candidates can be selected per vote.
4. **Session Management**: Users are automatically logged out after voting for security.
5. **Results Access**: Results visibility is controlled by administrators during active voting periods.

## 🛠️ Technical Support

For technical issues or password resets, contact the system administrator.

**System URLs:**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/docs

---
*Generated for DFPTA E-Voting System - Department of Education, Camarines Sur*
*Database Reset Completed: 2025-07-28*
