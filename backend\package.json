{"name": "dfpta-voting-backend", "version": "2.0.0", "description": "Backend API for DFPTA E-Voting System", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write src/**/*.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "init-db": "node scripts/initDatabase.js", "migrate-districts": "node scripts/migrateUsersToDistricts.js", "migrate-municipality-system": "node scripts/migrateMunicipalitySystem.js", "reset-database": "node scripts/resetDatabaseComplete.js"}, "keywords": ["voting", "election", "dfpta", "express", "mongodb", "nodejs"], "author": "DFPTA Development Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "validator": "^13.11.0", "winston": "^3.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}