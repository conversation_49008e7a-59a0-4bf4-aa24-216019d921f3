<?php
session_start(); 

if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    header('Location: admin_login.php');
    exit;
}

include 'db_connection.php';

$sql = "SELECT c.candidate_name, COUNT(v.vote_id) AS total_votes
        FROM candidates c
        LEFT JOIN votes v ON c.candidate_id = v.candidate_id
        GROUP BY c.candidate_name
        ORDER BY total_votes DESC, c.candidate_name ASC";

$result = $conn->query($sql);

// Initialize arrays for top candidates
$top_candidates = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $top_candidates[] = $row; // Store all candidates in one array
    }
}
$_SESSION['top_candidates'] = $top_candidates;

// Close the database connection
$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>DFPTA E-Voting System</title>
    <link rel="stylesheet" type="text/css" href="top35-sample.css"> <!-- Link to your CSS file -->
    <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@400;700&display=swap" rel="stylesheet">
    <link rel="icon" href="dfptalogo.png" type="image/x-icon"> 
</head>

<header class="header">
    <div class="container-nav">
        <div class="logo-container">
            <img src="dfptalogo.png" alt="Online Voting System Logo" class="logo">
            <b> DFPTA - SDO CAMARINES SUR </b>
        </div>
        <nav class="nav-center">
            <a class="nav-button" href="admin_panel-sample.php">DASHBOARD</a>
            <a class="nav-button" href="admin_logout.php">LOGOUT</a>
        </nav>
    </div>
</header>

<body>
    <section class="form-section">
        <div class="form-container">
                 <!-- Title for the Election Result -->
                 <h2 style="margin-bottom: -24px; color: #2d4863;  text-align: center; font-size: 26px; font-weight: bold;">
                    2024 DFPTA Election Result</h2>

            <div class="candidates-wrapper">
                <!-- First Form for the First 12 Candidates -->
                <div class="candidate-form">
                    <table>
                        <tr>
                            <th>No</th>
                            <th>Candidate Name</th>
                            <th>Total Votes</th>
                        </tr>
                        <?php
                        if (!empty($top_candidates)) {
                            for ($i = 0; $i < 12 && $i < count($top_candidates); $i++) {
                                $row = $top_candidates[$i];
                                echo "<tr>";
                                echo "<td>" . ($i + 1) . "</td>"; // Numbering starts from 1
                                echo "<td>" . $row["candidate_name"] . "</td>";
                                echo "<td>" . $row["total_votes"] . "</td>";
                                echo "</tr>";
                            }
                        } else {
                            echo "<tr><td colspan='3'>No results available yet.</td></tr>";
                        }
                        ?>
                    </table>
                </div>

                <!-- Second Form for the Next 12 Candidates -->
                <div class="candidate-form">
                    <table>
                        <tr>
                            <th>No</th>
                            <th>Candidate Name</th>
                            <th>Total Votes</th>
                        </tr>
                        <?php
                        if (!empty($top_candidates)) {
                            for ($i = 12; $i < 24 && $i < count($top_candidates); $i++) {
                                $row = $top_candidates[$i];
                                echo "<tr>";
                                echo "<td>" . ($i + 1) . "</td>"; // Start numbering from 13
                                echo "<td>" . $row["candidate_name"] . "</td>";
                                echo "<td>" . $row["total_votes"] . "</td>";
                                echo "</tr>";
                            }
                        }
                        ?>
                    </table>
                </div>

                <!-- Third Form for the Next 11 Candidates -->
                <div class="candidate-form">
                    <table>
                        <tr>
                            <th>No</th>
                            <th>Candidate Name</th>
                            <th>Total Votes</th>
                        </tr>
                        <?php
                        if (!empty($top_candidates)) {
                            for ($i = 24; $i < 35 && $i < count($top_candidates); $i++) {
                                $row = $top_candidates[$i];
                                echo "<tr>";
                                echo "<td>" . ($i + 1) . "</td>"; // Start numbering from 25
                                echo "<td>" . $row["candidate_name"] . "</td>";
                                echo "<td>" . $row["total_votes"] . "</td>";
                                echo "</tr>";
                            }
                        }
                        ?>
                    </table>
                </div>
            </div>
        </div>
    </section>
</body>

<footer class="footer">
        <div class="container-footer">
            <div class="footer-text">
                &copy; 2024 Social Mobilization and Networking - SDO Camarines Sur. All rights reserved.
            </div>
        </div>
    </footer>
</html>
