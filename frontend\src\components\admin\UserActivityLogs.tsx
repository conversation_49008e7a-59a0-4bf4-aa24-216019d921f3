import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Activity, 
  Search, 
  Filter, 
  LogIn, 
  LogOut, 
  Vote, 
  UserPlus, 
  Settings,
  RefreshCw
} from 'lucide-react'

interface ActivityLog {
  id: string
  userId: string
  username: string
  municipality: string
  action: 'login' | 'logout' | 'vote' | 'user_created' | 'settings_changed' | 'data_export'
  description: string
  timestamp: Date
  ipAddress: string
  userAgent: string
}

interface UserActivityLogsProps {
  className?: string
}

export function UserActivityLogs({ className }: UserActivityLogsProps) {
  const [logs, setLogs] = useState<ActivityLog[]>([])
  const [filteredLogs, setFilteredLogs] = useState<ActivityLog[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)

  // Generate mock activity logs
  useEffect(() => {
    const generateMockLogs = (): ActivityLog[] => {
      const actions = [
        { action: 'login' as const, description: 'User logged in' },
        { action: 'logout' as const, description: 'User logged out' },
        { action: 'vote' as const, description: 'Cast vote for municipality' },
        { action: 'user_created' as const, description: 'New user account created' },
        { action: 'settings_changed' as const, description: 'System settings updated' },
        { action: 'data_export' as const, description: 'Exported voting results' },
      ]

      const municipalities = ['Cabusao', 'Pili', 'Baao', 'Nabua', 'Bula', 'Iriga City']
      const usernames = ['admin', 'cabusao', 'pili', 'baao', 'nabua', 'bula', 'iriga']

      return Array.from({ length: 50 }, (_, i) => {
        const randomAction = actions[Math.floor(Math.random() * actions.length)]
        const randomMunicipality = municipalities[Math.floor(Math.random() * municipalities.length)]
        const randomUsername = usernames[Math.floor(Math.random() * usernames.length)]
        
        return {
          id: `log-${i + 1}`,
          userId: `user-${i + 1}`,
          username: randomUsername,
          municipality: randomMunicipality,
          action: randomAction.action,
          description: randomAction.description,
          timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Last 7 days
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        }
      }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    }

    setLogs(generateMockLogs())
  }, [])

  // Filter logs based on search and action filter
  useEffect(() => {
    let filtered = logs

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.municipality.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (actionFilter !== 'all') {
      filtered = filtered.filter(log => log.action === actionFilter)
    }

    setFilteredLogs(filtered)
  }, [logs, searchTerm, actionFilter])

  const refreshLogs = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsLoading(false)
  }

  const getActionIcon = (action: ActivityLog['action']) => {
    switch (action) {
      case 'login':
        return <LogIn className="h-4 w-4 text-green-600" />
      case 'logout':
        return <LogOut className="h-4 w-4 text-red-600" />
      case 'vote':
        return <Vote className="h-4 w-4 text-blue-600" />
      case 'user_created':
        return <UserPlus className="h-4 w-4 text-purple-600" />
      case 'settings_changed':
        return <Settings className="h-4 w-4 text-orange-600" />
      case 'data_export':
        return <Activity className="h-4 w-4 text-indigo-600" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getActionBadge = (action: ActivityLog['action']) => {
    const variants = {
      login: 'default',
      logout: 'secondary',
      vote: 'default',
      user_created: 'secondary',
      settings_changed: 'outline',
      data_export: 'outline',
    } as const

    return (
      <Badge variant={variants[action] || 'outline'} className="text-xs">
        {action.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) {
      return `${minutes}m ago`
    } else if (hours < 24) {
      return `${hours}h ago`
    } else {
      return `${days}d ago`
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            User Activity Logs
            <Badge variant="secondary">{filteredLogs.length}</Badge>
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshLogs}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by username, municipality, or action..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={actionFilter} onValueChange={setActionFilter}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              <SelectItem value="login">Login</SelectItem>
              <SelectItem value="logout">Logout</SelectItem>
              <SelectItem value="vote">Vote</SelectItem>
              <SelectItem value="user_created">User Created</SelectItem>
              <SelectItem value="settings_changed">Settings</SelectItem>
              <SelectItem value="data_export">Data Export</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Activity Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Action</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Municipality</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>IP Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    No activity logs found
                  </TableCell>
                </TableRow>
              ) : (
                filteredLogs.slice(0, 20).map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getActionIcon(log.action)}
                        {getActionBadge(log.action)}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{log.username}</TableCell>
                    <TableCell>{log.municipality}</TableCell>
                    <TableCell className="max-w-xs truncate">{log.description}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatTimestamp(log.timestamp)}
                    </TableCell>
                    <TableCell className="font-mono text-xs">{log.ipAddress}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {filteredLogs.length > 20 && (
          <div className="mt-4 text-center">
            <Button variant="outline" size="sm">
              Load More ({filteredLogs.length - 20} remaining)
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
