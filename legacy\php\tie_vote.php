<?php
session_start();

// Check if the admin is authenticated
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    // Redirect to the admin login page if not authenticated
    header('Location: admin_login.php');
    exit;
}

// Database connection and vote results retrieval
include 'db_connection.php';

// Get tied candidates from the session
if (!isset($_SESSION['top_15_candidates'])) {
    header('Location: admin_panel.php');
    exit;
}

$candidates = $_SESSION['top_15_candidates'];

usort($candidates, function ($a, $b) {
    return $b['total_votes'] - $a['total_votes'];
});

$minVotes15 = $candidates[14]['total_votes'];
$tieCandidates15 = array_filter($candidates, function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
});

$tieBeyond15 = count(array_filter(array_slice($candidates, 15), function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
})) > 0;

$isTie = count($tieCandidates15) > 1 || $tieBeyond15;
$tiedCandidates = $isTie ? array_values($tieCandidates15) : [];

// Generate a comma-separated list of tied candidate names for the SQL query
$tiedCandidateNames = implode("', '", array_column($tiedCandidates, 'candidate_name'));

// SQL query to retrieve vote results only for tied candidates
$sql = "SELECT c.candidate_name, COUNT(tv.vote_id) AS total_votes
        FROM candidates c
        LEFT JOIN tie_votes tv ON c.candidate_id = tv.candidate_id
        WHERE c.candidate_name IN ('$tiedCandidateNames')
        GROUP BY c.candidate_name
        ORDER BY total_votes DESC";

$result = $conn->query($sql);

// The tied candidates
$tied_candidates = [];
if ($result->num_rows > 0) {
    $count = 1; // Initialize count
    while ($row = $result->fetch_assoc()) {
        $tied_candidates[] = $row; // Corrected to include the entire row
        $count++;
    }
}

// Determine the maximum number of votes allowed
$voteFrequency = array_count_values(array_column(array_slice($candidates, 0, 15), 'total_votes'));
$maxVotesAllowed = $isTie ? $voteFrequency[$minVotes15] : 1;

// Close the database connection
$conn->close();
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vote Page</title>
    <link rel="stylesheet" type="text/css" href="tie_vote.css">
    




    <script>
    function updateCheckboxState() {
        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
        var maxVotesAllowed = <?php echo $maxVotesAllowed; ?>;
        var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');

        if (checkedCheckboxes.length > maxVotesAllowed) {
            alert('You can vote for up to ' + maxVotesAllowed + ' candidates only.');
            return false;
        }

        // Set or remove the "required" attribute based on the number of selected checkboxes
        checkboxes.forEach(function (checkbox) {
            checkbox.required = (checkedCheckboxes.length === 0);
        });

        return true;
    }

    function limitCheckboxes() {
        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].addEventListener('change', function () {
                var checkedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                var maxVotesAllowed = <?php echo $maxVotesAllowed; ?>;
                
                if (checkedCheckboxes.length > maxVotesAllowed) {
                    alert('You can vote for up to ' + maxVotesAllowed + ' candidates only.');
                    this.checked = false;
                }

                // Set or remove the "required" attribute based on the number of selected checkboxes
                checkboxes.forEach(function (checkbox) {
                    checkbox.required = (checkedCheckboxes.length === 0);
                });
            });
        }
    }

    window.onload = function () {
        limitCheckboxes();
    };
</script>






</head>
<body>
    <header>
        <h1>Vote Page</h1>
    </header>

    <section>
        <form action="tie_panel.php" method="post" onsubmit="return updateCheckboxState()">
            <p>Please select your preferred candidate(s):</p>
            <div class="candidates">
                <?php foreach ($tiedCandidates as $tiedCandidate): ?>
                    <div>
                        <input type='checkbox' name='selected_candidates[]' value='<?php echo $tiedCandidate['candidate_name']; ?>' required>
                        <label><?php echo $tiedCandidate['candidate_name']; ?></label>
                    </div>
                <?php endforeach; ?>
            </div>
            <p>You can vote up to <?php echo $maxVotesAllowed; ?> candidate(s).</p>
            <button type="submit">Vote</button>
        </form>
    </section>

    <footer>
        <div class="footer-row">
            <p>&copy; made by gien</p>
        </div>
    </footer>
</body>
</html>
