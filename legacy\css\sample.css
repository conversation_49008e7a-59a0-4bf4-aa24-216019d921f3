/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body {
    font-family: Tungsten-Bold, Arial, sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container-nav, .container-footer, .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header {
    background: #D3DCE5;
    padding: 25px 0;
}

.logo-container {
    display: flex;
    align-items: center;
}

.phone-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-center {
    display: flex;
    gap: 30px;
}

.logo {
    max-width: 70px;
    height: auto;
    display: block;
}

.content {
    background-image: url('ff/bgg1.png');
    background-repeat: no-repeat;
    background-position: center;
    color: #2D4863;
    text-align: center;
    padding: 35px 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.content-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
}

.content-text {
    text-align: center;
}

.section-title {
    font-size: 65px;
    margin-bottom: 20px;
}

.section-description {
    text-align: center;
    font-size: 23px;
}

.login-center {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.login-button {
    background-color: #2F984D;
    color: #fff;
    font-weight: bold;
    border: none;
    padding: 15px 30px;
    border-radius: 2px;
    transition: background-color 0.3s;
    margin-top: 20px;
}

.login-button:hover {
    background-color: #E98E53;
}

.footer {
    background: #fff;
    text-align: center;
    padding: 5px 0;
}

.footer-text {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
}

body, html {
    margin: 0;
    font: bold 14px/1.4 'Arial', arial, sans-serif;
    background: #000;
}

a {
    color: #243328;
    text-transform: uppercase;
    text-decoration: none;
    letter-spacing: 0.15em;
    display: inline-block;
    padding: 15px 20px;
    position: relative;
}

a:after {    
    background: none repeat scroll 0 0 transparent;
    bottom: 0;
    content: "";
    display: block;
    height: 2px;
    left: 50%;
    position: absolute;
    background: #243328;
    transition: width 0.3s ease 0s, left 0.3s ease 0s;
    width: 0;
}

a:hover:after { 
    width: 100%; 
    left: 0; 
}

/* Media queries for responsiveness */
@media screen and (max-width: 768px) {
    .container-nav, .container-footer, .container {
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-center {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .content-container {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 48px;
    }

    .section-description {
        font-size: 18px;
    }

    .login-button {
        padding: 10px 20px;
    }
}

@media screen and (max-width: 480px) {
    .section-title {
        font-size: 36px;
    }

    .section-description {
        font-size: 16px;
    }

    .login-button {
        padding: 8px 16px;
    }
}

@media screen and (max-height: 300px) {
    ul {
        margin-top: 40px;
    }
}
