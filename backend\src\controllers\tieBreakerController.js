import asyncHandler from 'express-async-handler'
import mongoose from 'mongoose'
import TieBreaker from '../models/TieBreaker.js'
import Vote from '../models/Vote.js'
import Candidate from '../models/Candidate.js'
import VotingSession from '../models/VotingSession.js'
import User from '../models/User.js'
import logger from '../utils/logger.js'

// @desc    Get active tie-breaker
// @route   GET /api/tiebreaker
// @access  Private (voter, admin, execom, tie-breaker)
export const getActiveTieBreaker = asyncHandler(async (req, res) => {
  const activeTieBreaker = await TieBreaker.getActiveTieBreaker()

  if (!activeTieBreaker) {
    return res.status(200).json({
      success: true,
      data: {
        isActive: false,
        message: 'No active tie-breaker round',
      },
    })
  }

  // Check if current user can participate
  const userId = req.user.id
  const canParticipate = activeTieBreaker.canVoterParticipate(userId)

  res.status(200).json({
    success: true,
    data: {
      isActive: true,
      tieBreaker: {
        id: activeTieBreaker._id,
        round: activeTieBreaker.round,
        tieBreakerRound: activeTieBreaker.tieBreakerRound,
        tiePosition: activeTieBreaker.tiePosition,
        description: activeTieBreaker.description,
        deadline: activeTieBreaker.deadline,
        totalVotesRequired: activeTieBreaker.totalVotesRequired,
        totalVotesCast: activeTieBreaker.totalVotesCast,
        progressPercentage: activeTieBreaker.progressPercentage,
        timeRemaining: activeTieBreaker.timeRemaining,
        canParticipate,
        hasVoted: !canParticipate,
        tiedCandidates: activeTieBreaker.tiedCandidates.map(tc => ({
          candidateId: tc.candidateId._id,
          candidateName: tc.candidateId.name,
          municipality: tc.candidateId.municipality,
          originalVotes: tc.originalVotes,
          tieBreakerVotes: tc.tieBreakerVotes,
          isWinner: tc.isWinner,
          isEliminated: tc.isEliminated,
        })),
      },
    },
  })
})

// @desc    Submit tie-breaker vote
// @route   POST /api/tiebreaker/vote
// @access  Private (voter, admin, execom, tie-breaker)
export const submitTieBreakerVote = asyncHandler(async (req, res) => {
  const { candidateId } = req.body
  const userId = req.user.id
  const userAgent = req.get('User-Agent')
  const ipAddress = req.ip

  // Validation
  if (!candidateId) {
    res.status(400)
    throw new Error('Please select a candidate')
  }

  // Get active tie-breaker
  const activeTieBreaker = await TieBreaker.getActiveTieBreaker()

  if (!activeTieBreaker) {
    res.status(400)
    throw new Error('No active tie-breaker round')
  }

  // Check if user can participate
  if (!activeTieBreaker.canVoterParticipate(userId)) {
    res.status(400)
    throw new Error('You have already voted in this tie-breaker round or are not eligible')
  }

  // Validate candidate is in the tied candidates list
  const tiedCandidate = activeTieBreaker.tiedCandidates.find(
    tc => tc.candidateId.toString() === candidateId.toString()
  )

  if (!tiedCandidate) {
    res.status(400)
    throw new Error('Selected candidate is not part of this tie-breaker')
  }

  // Get user details
  const user = await User.findById(userId)
  if (!user) {
    res.status(404)
    throw new Error('User not found')
  }

  // Start transaction
  const session = await mongoose.startSession()
  session.startTransaction()

  try {
    // Create vote record
    const vote = new Vote({
      voterId: userId,
      candidateId: candidateId,
      voteType: 'tiebreaker',
      round: activeTieBreaker.round,
      tieBreakerRound: activeTieBreaker.tieBreakerRound,
      sessionId: activeTieBreaker.sessionId,
      voterMunicipality: user.municipality,
      candidateMunicipality: tiedCandidate.candidateId.municipality,
      ipAddress,
      userAgent,
    })

    await vote.save({ session })

    // Add vote to tie-breaker
    await activeTieBreaker.addVote(userId, candidateId, user.municipality)

    // Update user voting status
    const tieBreakerKey = `tiebreaker${activeTieBreaker.tieBreakerRound}`
    user.votingRounds.hasVotedInRound[tieBreakerKey] = true
    user.votingRounds.lastVotedRound = activeTieBreaker.round
    user.votingRounds.lastVotedAt = new Date()
    await user.save({ session })

    // Update candidate vote count
    const candidate = await Candidate.findById(candidateId)
    if (candidate) {
      await candidate.addVoteForRound(
        activeTieBreaker.round,
        'tiebreaker',
        1
      )
    }

    await session.commitTransaction()

    logger.info(
      `User ${user.municipality} submitted tie-breaker vote for candidate ${candidateId} in round ${activeTieBreaker.tieBreakerRound}`
    )

    res.status(201).json({
      success: true,
      data: {
        message: 'Tie-breaker vote submitted successfully',
        tieBreakerRound: activeTieBreaker.tieBreakerRound,
        candidateId,
        submittedAt: new Date(),
      },
    })
  } catch (error) {
    await session.abortTransaction()
    logger.error('Tie-breaker vote submission failed:', error.message)
    throw error
  } finally {
    session.endSession()
  }
})

// @desc    Get tie-breaker results
// @route   GET /api/tiebreaker/results/:id
// @access  Private (voter, admin, execom, tie-breaker)
export const getTieBreakerResults = asyncHandler(async (req, res) => {
  const { id } = req.params

  const tieBreaker = await TieBreaker.findById(id)
    .populate('tiedCandidates.candidateId', 'name municipality')
    .populate('winnerCandidateId', 'name municipality')

  if (!tieBreaker) {
    res.status(404)
    throw new Error('Tie-breaker not found')
  }

  const results = tieBreaker.getCurrentResults()

  res.status(200).json({
    success: true,
    data: {
      tieBreaker: {
        id: tieBreaker._id,
        round: tieBreaker.round,
        tieBreakerRound: tieBreaker.tieBreakerRound,
        status: tieBreaker.status,
        tiePosition: tieBreaker.tiePosition,
        description: tieBreaker.description,
        startedAt: tieBreaker.startedAt,
        completedAt: tieBreaker.completedAt,
        totalVotesRequired: tieBreaker.totalVotesRequired,
        totalVotesCast: tieBreaker.totalVotesCast,
        progressPercentage: tieBreaker.progressPercentage,
        winner: tieBreaker.winnerCandidateId ? {
          candidateId: tieBreaker.winnerCandidateId._id,
          name: tieBreaker.winnerCandidateId.name,
          municipality: tieBreaker.winnerCandidateId.municipality,
          votes: tieBreaker.winnerVotes,
        } : null,
      },
      results,
    },
  })
})

// @desc    Detect and create tie-breaker (Admin only)
// @route   POST /api/admin/tiebreaker/detect
// @access  Private (admin only)
export const detectAndCreateTieBreaker = asyncHandler(async (req, res) => {
  const { round = 1, position = 15 } = req.body
  const createdBy = req.user.id

  // Get current voting session
  const currentSession = await VotingSession.getCurrentSession()

  if (!currentSession) {
    res.status(400)
    throw new Error('No active voting session')
  }

  // Check for ties
  const tieData = await TieBreaker.checkForNewTies(round, position)

  if (!tieData) {
    return res.status(200).json({
      success: true,
      data: {
        hasTie: false,
        message: 'No ties detected at the specified position',
      },
    })
  }

  // Check if tie-breaker already exists for this scenario
  const existingTieBreaker = await TieBreaker.findOne({
    round,
    tiePosition: position,
    sessionId: currentSession._id,
    status: { $in: ['pending', 'active'] },
  })

  if (existingTieBreaker) {
    res.status(400)
    throw new Error('Tie-breaker already exists for this position and round')
  }

  // Create new tie-breaker
  const tieBreakerRound = await TieBreaker.countDocuments({
    sessionId: currentSession._id,
  }) + 1

  const deadline = new Date()
  deadline.setHours(deadline.getHours() + 24) // 24 hours from now

  const tieBreaker = await TieBreaker.createTieBreaker({
    round,
    tieBreakerRound,
    tiedCandidates: tieData.candidates,
    tiePosition: position,
    sessionId: currentSession._id,
    createdBy,
    totalVotesRequired: 35, // All municipalities
    deadline,
  })

  // Update voting session
  await currentSession.startTieBreaker(tieBreaker._id, createdBy)

  logger.info(
    `Tie-breaker created for round ${round}, position ${position} with ${tieData.candidates.length} tied candidates`
  )

  res.status(201).json({
    success: true,
    data: {
      hasTie: true,
      tieBreaker: {
        id: tieBreaker._id,
        round: tieBreaker.round,
        tieBreakerRound: tieBreaker.tieBreakerRound,
        tiePosition: tieBreaker.tiePosition,
        tiedCandidatesCount: tieBreaker.tiedCandidates.length,
        deadline: tieBreaker.deadline,
        status: tieBreaker.status,
      },
      message: `Tie-breaker created for ${tieData.candidates.length} candidates tied at position ${position}`,
    },
  })
})

// @desc    Start tie-breaker (Admin only)
// @route   POST /api/admin/tiebreaker/:id/start
// @access  Private (admin only)
export const startTieBreaker = asyncHandler(async (req, res) => {
  const { id } = req.params
  const startedBy = req.user.id

  const tieBreaker = await TieBreaker.findById(id)

  if (!tieBreaker) {
    res.status(404)
    throw new Error('Tie-breaker not found')
  }

  if (tieBreaker.status !== 'pending') {
    res.status(400)
    throw new Error('Tie-breaker is not in pending status')
  }

  await tieBreaker.start()

  logger.info(`Tie-breaker ${id} started by user ${startedBy}`)

  res.status(200).json({
    success: true,
    data: {
      message: 'Tie-breaker started successfully',
      tieBreaker: {
        id: tieBreaker._id,
        status: tieBreaker.status,
        startedAt: tieBreaker.startedAt,
      },
    },
  })
})

// @desc    Complete tie-breaker (Admin only)
// @route   POST /api/admin/tiebreaker/:id/complete
// @access  Private (admin only)
export const completeTieBreaker = asyncHandler(async (req, res) => {
  const { id } = req.params
  const { winnerCandidateId } = req.body
  const completedBy = req.user.id

  const tieBreaker = await TieBreaker.findById(id)

  if (!tieBreaker) {
    res.status(404)
    throw new Error('Tie-breaker not found')
  }

  if (tieBreaker.status !== 'active') {
    res.status(400)
    throw new Error('Tie-breaker is not active')
  }

  // If no winner specified, determine from votes
  let winner = winnerCandidateId
  let winnerVotes = 0

  if (!winner) {
    const results = tieBreaker.getCurrentResults()
    if (results.length > 0) {
      const topCandidate = results[0]
      winner = topCandidate.candidateId
      winnerVotes = topCandidate.tieBreakerVotes
    }
  } else {
    const winnerCandidate = tieBreaker.tiedCandidates.find(
      tc => tc.candidateId.toString() === winner.toString()
    )
    winnerVotes = winnerCandidate ? winnerCandidate.tieBreakerVotes : 0
  }

  if (!winner) {
    res.status(400)
    throw new Error('No winner could be determined')
  }

  await tieBreaker.complete(winner, winnerVotes)

  // Update voting session
  const votingSession = await VotingSession.findById(tieBreaker.sessionId)
  if (votingSession) {
    await votingSession.completeTieBreaker(tieBreaker._id, completedBy)
  }

  logger.info(`Tie-breaker ${id} completed by user ${completedBy}, winner: ${winner}`)

  res.status(200).json({
    success: true,
    data: {
      message: 'Tie-breaker completed successfully',
      tieBreaker: {
        id: tieBreaker._id,
        status: tieBreaker.status,
        completedAt: tieBreaker.completedAt,
        winnerCandidateId: winner,
        winnerVotes,
      },
    },
  })
})
