<?php
// Connect to the database (replace with your actual database credentials)

$servername = "localhost";
$username = "gien";
$password = "f4)DlH-cEI";
$dbname = "voting_system";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Retrieve and display vote results
$sql = "SELECT c.name AS candidate_id, COUNT(v.id) AS total_votes
        FROM candidates c
        LEFT JOIN votes v ON c.id = v.candidate_id
        GROUP BY c.id, c.name
        ORDER BY total_votes DESC";

$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr>";
    echo "<th>Candidate</th>";
    echo "<th>Total Votes</th>";
    echo "</tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row["candidate_id"] . "</td>";
        echo "<td>" . $row["votes_id"] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No vote results available.";
}

// Close the database connection
$conn->close();
?>
