# DFPTA E-Voting System - <PERSON>R<PERSON> Stack

A modern, secure voting system for the DFPTA (Department of Education - Camarines Sur) built with MongoDB, Express.js, React, and Node.js.

## 🏗️ Project Structure

```
dfpta/
├── backend/                 # Node.js/Express API server
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── models/         # MongoDB/Mongoose models
│   │   ├── routes/         # API routes
│   │   ├── middleware/     # Custom middleware
│   │   ├── utils/          # Utility functions
│   │   ├── config/         # Configuration files
│   │   └── app.js          # Express app setup
│   ├── tests/              # Backend tests
│   ├── .env.example        # Environment variables template
│   └── package.json
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── utils/          # Utility functions
│   │   ├── types/          # TypeScript type definitions
│   │   └── App.tsx         # Main App component
│   ├── public/             # Static assets
│   ├── .env.example        # Environment variables template
│   └── package.json
├── docs/                   # Documentation
├── scripts/                # Utility scripts
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (v6 or higher)
- npm or yarn

### Development Setup

1. **<PERSON>lone and setup the project:**
   ```bash
   git clone <repository-url>
   cd dfpta
   ```

2. **Backend setup:**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your configuration
   npm run dev
   ```

3. **Frontend setup:**
   ```bash
   cd frontend
   npm install
   cp .env.example .env
   # Edit .env with your configuration
   npm run dev
   ```

## 🔧 Environment Configuration

### Backend (.env)
```
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/dfpta_voting
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### Frontend (.env)
```
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=DFPTA E-Voting System
VITE_APP_VERSION=2.0.0
```

## 🏛️ System Architecture

### User Roles
- **Voters**: Municipality representatives (35 municipalities)
- **Admin**: System administrators with full access
- **Execom**: Executive committee members
- **Tie-breaker**: Special role for tie-breaking votes

### Voting Process
1. **Regular Voting**: Voters select up to 15 candidates
2. **Top 15 Selection**: System determines top 15 candidates
3. **Tie-breaking**: Handle ties for 15th position
4. **Officer Selection**: Execom votes for officers (Chair, Vice-chair, Secretary, Treasurer)

## 🔐 Security Features

- JWT-based authentication with refresh tokens
- Password hashing with bcrypt
- Role-based access control (RBAC)
- Input validation and sanitization
- Rate limiting
- CORS configuration
- Security headers with Helmet.js
- Audit logging

## 📊 Database Schema

### Collections
- **users**: User accounts and authentication
- **candidates**: Candidate information
- **votes**: Regular voting records
- **tieVotes**: Tie-breaking votes
- **execomMembers**: Executive committee members
- **auditLogs**: System audit trail

## 🧪 Testing

```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test
```

## 📚 API Documentation

API documentation is available at `/api/docs` when running in development mode.

## 🚀 Deployment

See [deployment guide](docs/deployment.md) for production setup instructions.

## 📝 Migration from Legacy System

This system replaces the legacy PHP-based voting system. See [migration guide](docs/migration.md) for data migration instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions, contact the development team or create an issue in the repository.