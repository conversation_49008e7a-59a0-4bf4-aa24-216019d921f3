import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { formatDateTime, getRoleColor, getStatusColor } from '@/lib/utils'
import { User } from '@/types'
import { Edit, Eye, RotateCcw, Trash2 } from 'lucide-react'
import { useState } from 'react'

interface UserManagementTableProps {
  users: User[]
  isLoading: boolean
  onEditUser: (user: User) => void
  onDeleteUser: (userId: string) => void
  onResetVote: (userId: string) => void
  onViewUser: (user: User) => void
  selectedUsers: string[]
  onSelectUser: (userId: string) => void
  onSelectAll: (selected: boolean) => void
}

export function UserManagementTable({
  users,
  isLoading,
  onEditUser,
  onDeleteUser,
  onResetVote,
  onViewUser,
  selectedUsers,
  onSelectUser,
  onSelectAll,
}: UserManagementTableProps) {
  const [deleteUserId, setDeleteUserId] = useState<string | null>(null)
  const [resetVoteUserId, setResetVoteUserId] = useState<string | null>(null)

  if (isLoading) {
    return (
      <div className='space-y-3'>
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className='h-16 w-full' />
        ))}
      </div>
    )
  }

  const allSelected = users.length > 0 && selectedUsers.length === users.length
  const someSelected = selectedUsers.length > 0 && selectedUsers.length < users.length

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className='w-12'>
              <input
                type='checkbox'
                checked={allSelected}
                ref={input => {
                  if (input) input.indeterminate = someSelected
                }}
                onChange={e => onSelectAll(e.target.checked)}
                className='rounded border-gray-300'
              />
            </TableHead>
            <TableHead>Municipality</TableHead>
            <TableHead>District</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Voting Status</TableHead>
            <TableHead>Last Login</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className='text-muted-foreground py-8 text-center'>
                No users found
              </TableCell>
            </TableRow>
          ) : (
            users.map(user => (
              <TableRow key={user.id}>
                <TableCell>
                  <input
                    type='checkbox'
                    checked={selectedUsers.includes(user.id)}
                    onChange={() => onSelectUser(user.id)}
                    className='rounded border-gray-300'
                  />
                </TableCell>
                <TableCell className='font-medium'>{user.municipality}</TableCell>
                <TableCell>
                  <Badge variant='outline' className='text-xs'>
                    {user.district}
                  </Badge>
                </TableCell>
                <TableCell>{user.username}</TableCell>
                <TableCell>
                  <Badge className={getRoleColor(user.role)}>{user.role}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={user.isActive ? 'success' : 'secondary'}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge className={getStatusColor(user.hasVoted)}>
                    {user.hasVoted ? 'Voted' : 'Pending'}
                  </Badge>
                </TableCell>
                <TableCell className='text-muted-foreground text-sm'>
                  {user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}
                </TableCell>
                <TableCell className='text-right'>
                  <div className='flex items-center justify-end space-x-2'>
                    <Button
                      variant='ghost'
                      size='icon'
                      onClick={() => onViewUser(user)}
                      title='View user details'
                    >
                      <Eye className='h-4 w-4' />
                    </Button>

                    <Button
                      variant='ghost'
                      size='icon'
                      onClick={() => onEditUser(user)}
                      title='Edit user'
                    >
                      <Edit className='h-4 w-4' />
                    </Button>

                    {user.hasVoted && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant='ghost' size='icon' title='Reset vote'>
                            <RotateCcw className='h-4 w-4' />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Reset Vote</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to reset the vote for {user.municipality}? This
                              action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => onResetVote(user.id)}
                              className='bg-orange-600 hover:bg-orange-700'
                            >
                              Reset Vote
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant='ghost' size='icon' title='Delete user'>
                          <Trash2 className='h-4 w-4 text-red-600' />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete User</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete {user.municipality}? This action cannot
                            be undone and will permanently remove the user and all associated data.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDeleteUser(user.id)}
                            className='bg-red-600 hover:bg-red-700'
                          >
                            Delete User
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
