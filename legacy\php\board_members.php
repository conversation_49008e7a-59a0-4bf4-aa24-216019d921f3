// Check if there are tied candidates at No. 15
if (isset($top_35_candidates[14])) {
    $minVotes15 = $top_35_candidates[14]['total_votes'];

    // Filter tied candidates at No. 15
    $tieCandidates15 = array_filter($top_35_candidates, function ($candidate) use ($minVotes15) {
        return $candidate['total_votes'] == $minVotes15;
    });

    // Sort tied candidates by total votes in descending order
    usort($tieCandidates15, function ($a, $b) {
        return $b['total_votes'] - $a['total_votes'];
    });

    // Extract top 15 candidates, including ties
    $top_15_candidates = array_slice($top_35_candidates, 0, 15);

    // If more candidates are tied at position 15, append them
    if (count($tieCandidates15) > 1) {
        foreach ($tieCandidates15 as $tiedCandidate) {
            if (!in_array($tiedCandidate, $top_15_candidates)) {
                $top_15_candidates[] = $tiedCandidate;
            }
        }
    }
} else {
    // Just get the top 15 if no ties
    $top_15_candidates = array_slice($top_35_candidates, 0, 15);
}

// Store top 15 candidates in session
$_SESSION['top_15_candidates'] = $top_15_candidates;
