import express from 'express'
import {
  getCandidates,
  getVotingStatus,
  submitVotes,
  getVotingHistory,
  getResults,
} from '../controllers/votingController.js'
import { protect, voterOnly } from '../middleware/auth.js'
import { validateVoteSubmission } from '../middleware/validation.js'
import rateLimit from 'express-rate-limit'

const router = express.Router()

// Rate limiting for voting endpoints
const votingRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    success: false,
    error: 'Too many voting attempts, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

const voteSubmissionRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 3, // limit each IP to 3 vote submissions per minute
  message: {
    success: false,
    error: 'Too many vote submissions, please wait before trying again',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

// Public routes
router.get('/results', getResults)

// Protected routes - require authentication
router.use(protect)

// Get all active candidates
router.get('/candidates', getCandidates)

// Get voting status for current user
router.get('/vote/status', votingRateLimit, getVotingStatus)

// Get user's voting history
router.get('/vote/history', getVotingHistory)

// Submit votes (voters only)
router.post('/vote', voterOnly, voteSubmissionRateLimit, validateVoteSubmission, submitVotes)

export default router
