# DFPTA Admin Dashboard Guide

## 🎯 Overview

The DFPTA Admin Dashboard is a comprehensive user management and voting analytics interface built with modern React components and connected to a MongoDB database. It provides full CRUD operations for managing the 35 municipality users and real-time voting statistics.

## 🚀 Quick Start

### 1. Database Setup

```bash
# Start MongoDB (ensure MongoDB is running)
mongod

# Initialize the database with default users
cd backend
npm run init-db
```

This will create:
- **1 Admin user**: `admin / socmob123`
- **35 Municipality users**: `{municipality} / {municipality}123` (e.g., `baao / baao123`)
- **2 Executive Committee users**: Sample execom users
- **1 Tie-breaker user**: `tiebreaker / tiebreaker123`

### 2. Start the Application

```bash
# Terminal 1 - Backend API
cd backend
npm run dev

# Terminal 2 - Frontend React App
cd frontend
npm run dev
```

### 3. Access Admin Dashboard

1. Navigate to `http://localhost:3000`
2. Click "Login" 
3. Use admin credentials: `admin / socmob123`
4. Access admin dashboard at `http://localhost:3000/admin`

## 🎨 Features

### 📊 Dashboard Statistics
- **Real-time metrics**: Total users, voted users, pending votes, voting progress
- **Auto-refresh**: Updates every 30 seconds
- **Visual indicators**: Color-coded cards with icons
- **Progress tracking**: Percentage completion of voting process

### 👥 User Management
- **Complete CRUD operations**: Create, Read, Update, Delete users
- **Advanced filtering**: By role, municipality, voting status
- **Search functionality**: Real-time search across usernames and municipalities
- **Bulk operations**: Activate, deactivate, delete multiple users
- **Vote management**: Reset individual or bulk votes

### 🔍 Data Table Features
- **Sortable columns**: Click headers to sort data
- **Pagination**: Navigate through large user lists
- **Selection**: Individual and bulk user selection
- **Action buttons**: Quick access to edit, delete, reset vote actions
- **Status indicators**: Visual badges for roles and voting status

### 🛡️ Security & Validation
- **Role-based access**: Admin-only access to dashboard
- **Form validation**: Comprehensive validation using Zod schemas
- **Confirmation dialogs**: Prevent accidental destructive actions
- **Input sanitization**: Secure data handling

## 🎛️ User Interface Components

### ShadCN UI Components Used
- **Cards**: Statistics display and content containers
- **Tables**: User data display with sorting and pagination
- **Dialogs**: User creation and editing forms
- **Alert Dialogs**: Confirmation for destructive actions
- **Buttons**: Various actions with proper variants
- **Badges**: Status and role indicators
- **Select**: Dropdown filters and form inputs
- **Input**: Search and form fields
- **Skeleton**: Loading states

### Theme System
- **Light/Dark modes**: Toggle between themes
- **DaisyUI integration**: Consistent color palette
- **Responsive design**: Mobile, tablet, desktop optimized
- **Accessibility**: WCAG 2.1 AA compliant

## 📋 User Management Operations

### Creating Users
1. Click "Add User" button
2. Fill required fields:
   - Username (unique, 3-50 characters)
   - Municipality (select from 35 options)
   - Password (min 6 chars, must include upper, lower, number)
   - Role (voter, admin, execom, tie-breaker)
   - Email (optional)
3. Click "Create User"

### Editing Users
1. Click edit icon in user row
2. Modify fields (username cannot be changed)
3. Toggle active status if needed
4. Click "Update User"

### Bulk Operations
1. Select multiple users using checkboxes
2. Choose bulk action:
   - **Activate**: Enable user accounts
   - **Deactivate**: Disable user accounts
   - **Delete**: Permanently remove users
   - **Reset Votes**: Clear voting status
3. Confirm action in dialog

### Filtering and Search
- **Search**: Type in search box to filter by username/municipality
- **Role filter**: Show only specific roles
- **Voting status**: Filter by voted/not voted
- **Real-time**: Results update as you type

## 🔧 Technical Implementation

### Backend API Endpoints
```
GET    /api/admin/dashboard          # Dashboard statistics
GET    /api/admin/users              # List users with pagination
POST   /api/admin/users              # Create new user
GET    /api/admin/users/:id          # Get user by ID
PUT    /api/admin/users/:id          # Update user
DELETE /api/admin/users/:id          # Delete user
POST   /api/admin/users/:id/reset-vote # Reset user vote
POST   /api/admin/users/bulk         # Bulk operations
```

### Frontend Architecture
```
src/
├── components/
│   ├── ui/                    # ShadCN UI components
│   └── admin/                 # Admin-specific components
│       ├── StatisticsCards.tsx
│       ├── UserManagementTable.tsx
│       └── UserForm.tsx
├── hooks/
│   └── useAdmin.ts            # Admin API hooks
├── services/
│   └── adminService.ts        # API service functions
└── pages/
    └── AdminDashboard.tsx     # Main dashboard page
```

### State Management
- **React Query**: Server state management with caching
- **React Hook Form**: Form state and validation
- **Local State**: UI state (modals, selections, filters)

## 📊 Database Schema

### User Model
```javascript
{
  username: String,           // Unique identifier
  municipality: String,       // One of 35 municipalities
  password: String,          // Hashed with bcrypt
  role: String,              // voter|admin|execom|tie-breaker
  email: String,             // Optional
  hasVoted: Boolean,         // Voting status
  isActive: Boolean,         // Account status
  lastLogin: Date,           // Last login timestamp
  createdAt: Date,           // Account creation
  updatedAt: Date            // Last modification
}
```

## 🔐 Security Features

### Authentication
- **JWT tokens**: Secure authentication with refresh tokens
- **Role-based access**: Admin-only dashboard access
- **Session management**: Automatic token refresh
- **Account lockout**: Protection against brute force attacks

### Data Protection
- **Input validation**: Server and client-side validation
- **SQL injection prevention**: MongoDB with Mongoose ODM
- **XSS protection**: Input sanitization
- **Rate limiting**: API endpoint protection

## 📱 Responsive Design

### Breakpoints
- **Mobile**: 320px+ (stacked layout, simplified navigation)
- **Tablet**: 768px+ (condensed table, touch-friendly buttons)
- **Desktop**: 1024px+ (full table layout, hover states)

### Accessibility
- **Keyboard navigation**: Full keyboard support
- **Screen readers**: Proper ARIA labels
- **Color contrast**: WCAG AA compliant
- **Focus indicators**: Clear focus states

## 🚨 Error Handling

### User Feedback
- **Toast notifications**: Success/error messages
- **Loading states**: Skeleton components during data fetch
- **Validation errors**: Real-time form validation
- **Confirmation dialogs**: Prevent accidental actions

### Error Recovery
- **Retry mechanisms**: Automatic retry for failed requests
- **Fallback UI**: Graceful degradation
- **Error boundaries**: Catch and handle React errors

## 📈 Performance Optimization

### Data Loading
- **Pagination**: Load data in chunks
- **Caching**: React Query caching strategy
- **Debounced search**: Reduce API calls
- **Optimistic updates**: Immediate UI feedback

### Bundle Optimization
- **Code splitting**: Lazy load admin components
- **Tree shaking**: Remove unused code
- **Compression**: Gzip compression enabled

## 🔄 Real-time Updates

### Auto-refresh
- **Dashboard stats**: Every 30 seconds
- **User list**: On data mutations
- **Cache invalidation**: Smart cache updates

## 🛠️ Development

### Adding New Features
1. **Backend**: Add API endpoints in `controllers/adminController.js`
2. **Frontend**: Create components in `components/admin/`
3. **Hooks**: Add API hooks in `hooks/useAdmin.ts`
4. **Types**: Update TypeScript types in `types/index.ts`

### Testing
```bash
# Backend tests
cd backend && npm test

# Frontend tests
cd frontend && npm test
```

## 📞 Support

### Common Issues
1. **Database connection**: Ensure MongoDB is running
2. **Authentication errors**: Check JWT secrets in .env
3. **Permission denied**: Verify admin role assignment
4. **UI not loading**: Check console for JavaScript errors

### Logs
- **Backend logs**: Check console output or log files
- **Frontend errors**: Browser developer console
- **Network issues**: Network tab in browser dev tools

This admin dashboard provides a complete solution for managing the DFPTA voting system with modern UI/UX and robust functionality.
