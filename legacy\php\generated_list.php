<?php
// Start the session if not already started
session_start();

// Check if the top 15 candidates are stored in the session
if (!isset($_SESSION['top_15_candidates'])) {
    header('Location: admin_panel.php'); // Redirect to admin panel if top 15 candidates are not available
    exit;
}

// Retrieve the top 15 candidates from the session
$candidates = $_SESSION['top_15_candidates'];

// Sort candidates by total votes in descending order
usort($candidates, function ($a, $b) {
    return $b['total_votes'] - $a['total_votes'];
});

// Check if there is a tie for the 15th position
$minVotes15 = $candidates[14]['total_votes']; // Assuming the 15th position
$tieCandidates15 = array_filter($candidates, function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
});

// Check if there is a tie beyond the 15th position
$tieBeyond15 = count(array_filter(array_slice($candidates, 15), function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
})) > 0;

// Check if there is a tie according to the specified conditions
$isTie = count($tieCandidates15) > 1 && $tieBeyond15;
$tiedCandidates = $isTie ? array_values($tieCandidates15) : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated List</title>
    <!-- Uncomment the line below if you have a separate CSS file -->
    <!-- <link rel="stylesheet" type="text/css" href="style.css"> -->
</head>
<body>
    <header>
        <h1>Generated List</h1>
    </header>

    <section>
        <form action="index.html" method="post">
            <p>Please select your preferred candidate(s):</p>
            <ul>
                <?php
                // Display checklist items based on tie verification results
                foreach ($tiedCandidates as $count => $candidate) {
                    echo "<li>";
                    echo "<input type='checkbox' name='selected_candidates[]' value='{$candidate['candidate_name']}'>";
                    echo "<label>{$candidate['candidate_name']}</label>";
                    echo "</li>";
                }
                ?>
            </ul>

            <button type="submit">Go to dashboard to proceed for voting</button>
        </form>
    </section>

    <footer>
        <div class="footer-row">
            <p>&copy; made by gien</p>
        </div>
    </footer>
</body>
</html>
