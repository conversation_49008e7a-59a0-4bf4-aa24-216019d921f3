import { useQuery } from 'react-query'
import { systemService, DistrictMunicipalityData, SystemConfig, MunicipalityDetails } from '../services/systemService'

/**
 * Hook to get districts and municipalities dynamically
 */
export function useDistrictsAndMunicipalities() {
  return useQuery<DistrictMunicipalityData, Error>(
    ['system', 'districts-municipalities'],
    () => systemService.getDistrictsAndMunicipalities(),
    {
      staleTime: 10 * 60 * 1000, // 10 minutes - this data doesn't change often
      cacheTime: 30 * 60 * 1000, // 30 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    }
  )
}

/**
 * Hook to get system configuration
 */
export function useSystemConfig() {
  return useQuery<SystemConfig, Error>(
    ['system', 'config'],
    () => systemService.getSystemConfig(),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 15 * 60 * 1000, // 15 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    }
  )
}

/**
 * Hook to get municipality details
 */
export function useMunicipalityDetails(municipalityName: string) {
  return useQuery<MunicipalityDetails, Error>(
    ['system', 'municipality', municipalityName],
    () => systemService.getMunicipalityDetails(municipalityName),
    {
      enabled: !!municipalityName,
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
    }
  )
}

/**
 * Hook to get district for municipality
 */
export function useDistrictForMunicipality(municipalityName: string) {
  return useQuery<string, Error>(
    ['system', 'district-for-municipality', municipalityName],
    () => systemService.getDistrictForMunicipality(municipalityName),
    {
      enabled: !!municipalityName,
      staleTime: 10 * 60 * 1000, // 10 minutes
      cacheTime: 30 * 60 * 1000, // 30 minutes
      retry: 2,
    }
  )
}

/**
 * Hook to get municipalities in district
 */
export function useMunicipalitiesInDistrict(district: string) {
  return useQuery<string[], Error>(
    ['system', 'municipalities-in-district', district],
    () => systemService.getMunicipalitiesInDistrict(district),
    {
      enabled: !!district,
      staleTime: 10 * 60 * 1000, // 10 minutes
      cacheTime: 30 * 60 * 1000, // 30 minutes
      retry: 2,
    }
  )
}

/**
 * Hook to get available roles
 */
export function useAvailableRoles() {
  return useQuery<string[], Error>(
    ['system', 'available-roles'],
    () => systemService.getAvailableRoles(),
    {
      staleTime: 15 * 60 * 1000, // 15 minutes
      cacheTime: 60 * 60 * 1000, // 1 hour
      retry: 2,
      refetchOnWindowFocus: false,
    }
  )
}

/**
 * Hook to get max candidates per vote
 */
export function useMaxCandidatesPerVote() {
  return useQuery<number, Error>(
    ['system', 'max-candidates-per-vote'],
    () => systemService.getMaxCandidatesPerVote(),
    {
      staleTime: 15 * 60 * 1000, // 15 minutes
      cacheTime: 60 * 60 * 1000, // 1 hour
      retry: 2,
      refetchOnWindowFocus: false,
    }
  )
}

/**
 * Custom hook for form validation helpers
 */
export function useSystemValidation() {
  const { data: districtMunicipalityData } = useDistrictsAndMunicipalities()

  const validateMunicipality = (municipalityName: string): boolean => {
    if (!districtMunicipalityData) return false
    return districtMunicipalityData.municipalities.includes(municipalityName)
  }

  const validateDistrict = (district: string): boolean => {
    if (!districtMunicipalityData) return false
    return districtMunicipalityData.districts.includes(district)
  }

  const getDistrictForMunicipality = (municipalityName: string): string | null => {
    if (!districtMunicipalityData) return null
    
    for (const [district, municipalities] of Object.entries(districtMunicipalityData.districtMapping)) {
      if (municipalities.includes(municipalityName)) {
        return district
      }
    }
    return null
  }

  const getMunicipalitiesInDistrict = (district: string): string[] => {
    if (!districtMunicipalityData) return []
    return districtMunicipalityData.districtMapping[district] || []
  }

  return {
    validateMunicipality,
    validateDistrict,
    getDistrictForMunicipality,
    getMunicipalitiesInDistrict,
    isLoading: !districtMunicipalityData,
  }
}
