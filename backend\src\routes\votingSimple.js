import express from 'express'
import { protect } from '../middleware/auth.js'
import Candidate from '../models/Candidate.js'
import User from '../models/User.js'
import logger from '../utils/logger.js'

const router = express.Router()

// @desc    Get all active candidates (municipalities)
// @route   GET /api/candidates
// @access  Private
router.get('/candidates', protect, async (req, res) => {
  try {
    const candidates = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes currentRank finalPosition isWinner isEliminated isActive')
      .sort({ municipalityName: 1 })

    res.status(200).json({
      success: true,
      data: {
        candidates,
        count: candidates.length
      }
    })
  } catch (error) {
    logger.error('Error fetching candidates:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while fetching candidates'
    })
  }
})

// @desc    Get voting status
// @route   GET /api/voting/status
// @access  Private
router.get('/voting/status', protect, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    const votingStatus = {
      canVote: !user.hasVoted && user.isActive,
      hasVoted: user.hasVoted,
      votingStatus: user.hasVoted ? 'completed' : 'round1',
      currentRound: 1,
      maxCandidatesPerVote: 15,
      userRole: user.role,
      municipality: user.municipality,
      district: user.district
    }

    res.status(200).json({
      success: true,
      data: votingStatus
    })
  } catch (error) {
    logger.error('Error fetching voting status:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while fetching voting status'
    })
  }
})

// @desc    Submit votes
// @route   POST /api/voting/submit
// @access  Private
router.post('/voting/submit', protect, async (req, res) => {
  try {
    const { candidateIds } = req.body
    const user = await User.findById(req.user.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    if (user.hasVoted) {
      return res.status(400).json({
        success: false,
        error: 'You have already voted'
      })
    }

    if (!candidateIds || !Array.isArray(candidateIds)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid candidate selection'
      })
    }

    if (candidateIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Please select at least one candidate'
      })
    }

    if (candidateIds.length > 15) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 15 candidates can be selected'
      })
    }

    // Verify all candidate IDs are valid
    const validCandidates = await Candidate.find({
      _id: { $in: candidateIds },
      isActive: true
    })

    if (validCandidates.length !== candidateIds.length) {
      return res.status(400).json({
        success: false,
        error: 'One or more invalid candidates selected'
      })
    }

    // Update vote counts for selected candidates
    await Candidate.updateMany(
      { _id: { $in: candidateIds } },
      { $inc: { totalVotes: 1 } }
    )

    // Mark user as voted
    await User.findByIdAndUpdate(req.user.id, {
      hasVoted: true,
      'votingRounds.hasVotedInRound.round1': true,
      'votingRounds.lastVotedRound': 1,
      'votingRounds.lastVotedAt': new Date()
    })

    logger.info(`User ${user.username} (${user.municipality}) submitted votes for ${candidateIds.length} candidates`)

    res.status(200).json({
      success: true,
      data: {
        message: 'Votes submitted successfully',
        votedCandidates: candidateIds.length,
        municipality: user.municipality,
        district: user.district
      }
    })
  } catch (error) {
    logger.error('Error submitting votes:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while submitting votes'
    })
  }
})

// @desc    Get voting results (basic)
// @route   GET /api/voting/results
// @access  Private
router.get('/voting/results', protect, async (req, res) => {
  try {
    const results = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes')
      .sort({ totalVotes: -1, municipalityName: 1 })

    // Calculate rankings
    const resultsWithRank = results.map((candidate, index) => ({
      ...candidate.toObject(),
      currentRank: index + 1
    }))

    // Get voting statistics
    const totalVoters = await User.countDocuments({ role: 'voter', isActive: true })
    const totalVotesCast = await User.countDocuments({ role: 'voter', hasVoted: true, isActive: true })
    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0

    res.status(200).json({
      success: true,
      data: {
        results: resultsWithRank,
        statistics: {
          totalVoters,
          totalVotesCast,
          participationRate: Math.round(participationRate * 100) / 100,
          totalCandidates: results.length
        }
      }
    })
  } catch (error) {
    logger.error('Error fetching results:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while fetching results'
    })
  }
})

export default router
