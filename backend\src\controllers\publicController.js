import SystemSettings from '../models/SystemSettings.js'
import logger from '../utils/logger.js'

/**
 * @desc    Get public results status
 * @route   GET /api/results/status
 * @access  Public
 */
export const getPublicResultsStatus = async (req, res) => {
  try {
    const enabled = await SystemSettings.getSetting('public_results_enabled')

    res.status(200).json({
      success: true,
      data: {
        enabled: enabled || false,
      },
    })

  } catch (error) {
    logger.error('Get public results status error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching public results status',
    })
  }
}

/**
 * @desc    Get public voting results (if enabled)
 * @route   GET /api/results
 * @access  Public
 */
export const getPublicResults = async (req, res) => {
  try {
    const enabled = await SystemSettings.getSetting('public_results_enabled')

    if (!enabled) {
      return res.status(403).json({
        success: false,
        error: 'Public results are not currently available',
        message: 'Results will be published once the election is complete',
      })
    }

    // TODO: Implement actual results fetching logic
    // For now, return a placeholder response
    res.status(200).json({
      success: true,
      data: {
        message: 'Public results feature is enabled but results data is not yet implemented',
        enabled: true,
      },
    })

  } catch (error) {
    logger.error('Get public results error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching public results',
    })
  }
}
