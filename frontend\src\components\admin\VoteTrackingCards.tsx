import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, Users, Vote, Activity } from 'lucide-react'

interface VoteTrackingStats {
  totalVotes: number
  totalVoters: number
  totalVotesCast: number
  participationRate: number
  totalCandidates: number
}

interface VoteTrackingCardsProps {
  stats: VoteTrackingStats | null
  isLoading: boolean
}

export function VoteTrackingCards({ stats, isLoading }: VoteTrackingCardsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">No voting data available</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const cards = [
    {
      title: 'Total Votes Cast',
      value: stats.totalVotes.toLocaleString(),
      description: `From ${stats.totalVotesCast} voters`,
      icon: Vote,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Participation Rate',
      value: `${stats.participationRate}%`,
      description: `${stats.totalVotesCast} of ${stats.totalVoters} voters`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Active Voters',
      value: stats.totalVotesCast.toLocaleString(),
      description: `Out of ${stats.totalVoters} registered`,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Total Candidates',
      value: stats.totalCandidates.toLocaleString(),
      description: 'Municipalities participating',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => {
        const Icon = card.icon
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">{card.description}</p>
              {card.title === 'Participation Rate' && (
                <div className="mt-2">
                  <Badge 
                    variant={stats.participationRate >= 75 ? 'default' : stats.participationRate >= 50 ? 'secondary' : 'destructive'}
                    className="text-xs"
                  >
                    {stats.participationRate >= 75 ? 'High' : stats.participationRate >= 50 ? 'Medium' : 'Low'} Turnout
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
