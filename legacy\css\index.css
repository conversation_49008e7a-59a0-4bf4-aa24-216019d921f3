/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body, html {
    font-family: 'Urbanist', Arial, sans-serif;
    background-color: #f7f7f7; 
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh; 
    overflow-x: hidden; 
}

/* Center and responsive container */
.container-nav, .container-footer, .container {
    width: 100%; 
    max-width: 1200px; 
    margin: 0 auto;
    padding: 0 10px; 
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header {
    background: #D3DCE5;
    padding: 25px 0; /*25*/
}

/* Logo and title */
.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 70px;
    display: block;
    height: auto;
    margin-right: 10px;
}

/* Center logo text */
.logo-text b {
    font-family: 'Urbanist', sans-serif;
    font-size: 18px;
    font-weight: bold;
    color: #243328;
}

/* Navigation styling */
.nav-center {
    display: flex;
    gap: 20px;
}

.nav-button {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    padding: 10px 15px;
    position: relative;
    text-transform: capitalize;
    letter-spacing: 0.15em;
}

.nav-button:hover {
    background: #d3dce5;
}

/* Content styling */
.content {
    background-image: url('ff/admin.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
    color: #2D4863;
  /*  padding: 10px 0; */
    padding: 32px 42px; 
    width: 100%;
    display: flex; /* flex*/
    justify-content: center; 
    align-items: center; 
    flex: 1; 
    text-align: center; 
    flex-direction: column; 
}

/* Center container for title and phone image */
.content-center {
    display: flex;
    justify-content: space-between;
    align-items: center; 
    gap: 20px; 
}
.section-title {
    font-size: clamp(32px, 8vw, 72px); 
    margin-top: -100px; 
    margin-bottom: 20px; 
    text-align: center; 
    margin-right: 10px;
}

/* Phone container styling */
.phone-container {
    display: flex; 
    justify-content: center; 
    align-items: center;
    margin-right: 70px;
    margin-top: -20px;
}

.phone-image {
    max-width: 100px; 
    height: auto;
}

.section-description {
    font-size: clamp(14px, 3vw, 24px); 
    text-align: center; 
    margin-top: 15px; 
    margin-bottom: 50px;
    padding: 0 20px; 
    font-weight: bold;
}

/* Login button */
.login-center {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.login-button {
    background-color: #2F984D;
    color: #fff;
    font-weight: bold;
    padding: 15px 30px;
    border-radius: 2px;
    transition: background-color 0.3s;
}

.login-button:hover {
   /* background-color: #E98E53; */
    background-color: #45dd64;
}

/* Footer styling */
.footer {
    background: #fff;
    padding: 5px 0;
    text-align: center; 
}

.container-footer {
    display: flex; 
    justify-content: center; 
    align-items: center; 
    width: 100%; 
}

.footer-text {
    display: flex; 
    justify-content: center;
    align-items: right; 
    font-size: 12px;
    color: #2d4863;
    margin: 0; 
}
/* Link underline effect */
a {
    color: #243328;
    position: relative;
    padding: 5px 10px;
    text-transform: uppercase;
    transition: color 0.3s;
}

a:after {
    content: "";
    position: absolute;
    height: 2px;
    left: 50%;
    bottom: 0;
    width: 0;
    background: #243328;
    transition: width 0.3s ease, left 0.3s ease;
}

a:hover:after {
    width: 100%;
    left: 0;
}

@media (max-width: 768px) {
    .logo-container {
        display: flex;
        flex-direction: column; 
        align-items: center;    
        justify-content: center;
        text-align: center;      
        width: 100%;      
        margin-top: 120px;     
    }

    /* Adjust logo size */
    .logo {
        width: 80px; 
        height: auto;
    }

    /* Hide the <b> tag in the logo text */
    .logo-container > b {
        display: none; 
    }


    .section-title {
        font-size: 22px; 
        text-align: center; 
        margin-top: -80px; 
    }

    .phone {
        display: none; 
    }

    .contact-text {
        display: none; 
    }

    .voting-instruction {
        display: none; 
    }
}

/* Media query for screens 480px wide and smaller */
@media (max-width: 480px) {
    /* Center align and vertically center the logo */
    .logo-container {
        display: flex;
        flex-direction: column; 
        align-items: center;    
        justify-content: center;
        text-align: center;      
        width: 100%;      
        margin-top: 120px;     
    }

    /* Adjust logo size */
    .logo {
        width: 80px; 
        height: auto;
    }

    /* Hide the <b> tag in the logo text */
    .logo-container > b {
        display: none; 
    }

    .section-title {
        font-size: 22px; 
        margin-top: -170px;
    }

    .section-description {
        font-size: 12px; 
    }

    .login-button {
        padding: 10px 20px; 
        font-size: 10px;    
    }

    /* Hide navigation buttons */
    .nav-button {
        display: none !important; 
    }

    .footer-text {
        display: none !important;
        overflow: hidden;
}}
