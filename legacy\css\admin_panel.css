/* admin_panel.css */

/* Reset default styles */
body, h1, h2, p, ul, li, a, form, label, input {
    margin: 0;
    padding: 0;
    text-decoration: none;
    box-sizing: border-box;
}

/* Global styles */
body {
    font-family: Arial, sans-serif;
    background-color: #f7f7f7;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin: 0;
}

/* Header styles */
.header {
    background: #243328;
    color: #fff;
    padding: 0;
    text-align: center;
}

/* Navigation bar styles */
.navigation {
    background: #fff;
    padding: 5px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    outline: 2px solid #243328;
    margin: 0;
}

.nav-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.nav-item {
    font-size: 18px;
    font-weight: bold;
}

.nav-link {
    color: #243328;
}

/* Table results container styles */
.table-results {
    display: inline-block;
    justify-content: center;
    align-items: center;
    min-height: 60vh; /* Set a minimum height to center the content vertically */
    margin-top: 20px; /* Adjust the top margin as needed */
}

/* Table styles */
table {
    width: 25%; /* Adjust the width as needed */
    margin: 20px auto; /* Center the table and adjust the top margin */
    border-collapse: collapse;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color:darkgreen;
    color: white;
    text-align: center;
}

/* Responsive styles for smaller screens */
@media (max-width: 768px) {
    th, td {
        padding: 6px;
    }
}

label {
    font-weight: bold;
    font-size: 12px;
    color: white;
}

/* Footer styles */
.footer {
    background: #fff;
    padding: 10px 5px;
    text-align: center;
    margin: 0;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    max-width: 50px;
    height: auto;
    margin-right: 10px;
}

.container-footer {
    font-size: 12px;
}

/* Media Query for Tablets and Smaller Screens */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
    }
    .nav-list {
        flex-direction: column;
        gap: 10px;
    }
}
