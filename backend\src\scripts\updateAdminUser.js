import mongoose from 'mongoose'
import User from '../models/User.js'
import logger from '../utils/logger.js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

/**
 * Script to update admin user profile
 * - Change municipality from "<PERSON>li" to "admin"
 * - Remove district field for admin users
 */
async function updateAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Connected to MongoDB for admin user update')

    // Find existing admin user
    const adminUser = await User.findOne({ role: 'admin' })

    if (!adminUser) {
      logger.info('No admin user found, creating new admin user')
      
      // Create new admin user with updated profile
      const newAdmin = new User({
        username: 'admin',
        municipality: 'admin',
        password: process.env.ADMIN_DEFAULT_PASSWORD || 'socmob123',
        role: 'admin',
        email: process.env.ADMIN_EMAIL,
      })

      await newAdmin.save()
      logger.info('✅ New admin user created successfully')
      
    } else {
      logger.info(`Found admin user: ${adminUser.username} (${adminUser.municipality})`)
      
      // Update admin user profile
      const updates = {}
      let hasChanges = false

      // Update municipality if needed
      if (adminUser.municipality !== 'admin') {
        updates.municipality = 'admin'
        hasChanges = true
        logger.info(`Updating municipality from "${adminUser.municipality}" to "admin"`)
      }

      // Remove district if it exists
      if (adminUser.district) {
        updates.$unset = { district: 1 }
        hasChanges = true
        logger.info(`Removing district field: "${adminUser.district}"`)
      }

      if (hasChanges) {
        await User.updateOne({ _id: adminUser._id }, updates)
        logger.info('✅ Admin user updated successfully')
        
        // Verify the update
        const updatedAdmin = await User.findById(adminUser._id)
        logger.info(`Updated admin profile:`)
        logger.info(`- Username: ${updatedAdmin.username}`)
        logger.info(`- Municipality: ${updatedAdmin.municipality}`)
        logger.info(`- District: ${updatedAdmin.district || 'None (removed)'}`)
        logger.info(`- Role: ${updatedAdmin.role}`)
      } else {
        logger.info('✅ Admin user profile is already up to date')
      }
    }

    // Verify final state
    const finalAdmin = await User.findOne({ role: 'admin' })
    if (finalAdmin) {
      logger.info('Final admin user state:')
      logger.info(`- ID: ${finalAdmin._id}`)
      logger.info(`- Username: ${finalAdmin.username}`)
      logger.info(`- Municipality: ${finalAdmin.municipality}`)
      logger.info(`- District: ${finalAdmin.district || 'None'}`)
      logger.info(`- Role: ${finalAdmin.role}`)
      logger.info(`- Active: ${finalAdmin.isActive}`)
    }

  } catch (error) {
    logger.error('Error updating admin user:', error)
    throw error
  } finally {
    await mongoose.disconnect()
    logger.info('Disconnected from MongoDB')
  }
}

/**
 * Script to validate admin user configuration
 */
async function validateAdminUser() {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Validating admin user configuration...')

    const adminUser = await User.findOne({ role: 'admin' })
    
    if (!adminUser) {
      logger.error('❌ No admin user found in database')
      return false
    }

    let isValid = true
    
    // Check municipality
    if (adminUser.municipality !== 'admin') {
      logger.error(`❌ Admin municipality should be "admin", found: "${adminUser.municipality}"`)
      isValid = false
    } else {
      logger.info(`✅ Admin municipality is correct: "${adminUser.municipality}"`)
    }
    
    // Check district (should not exist)
    if (adminUser.district) {
      logger.error(`❌ Admin should not have district, found: "${adminUser.district}"`)
      isValid = false
    } else {
      logger.info(`✅ Admin district is correctly undefined`)
    }
    
    // Check role
    if (adminUser.role !== 'admin') {
      logger.error(`❌ Admin role should be "admin", found: "${adminUser.role}"`)
      isValid = false
    } else {
      logger.info(`✅ Admin role is correct: "${adminUser.role}"`)
    }
    
    // Check active status
    if (!adminUser.isActive) {
      logger.error(`❌ Admin should be active, found: ${adminUser.isActive}`)
      isValid = false
    } else {
      logger.info(`✅ Admin is active: ${adminUser.isActive}`)
    }

    if (isValid) {
      logger.info('✅ Admin user configuration is valid')
    } else {
      logger.error('❌ Admin user configuration has issues')
    }

    return isValid

  } catch (error) {
    logger.error('Error validating admin user:', error)
    return false
  } finally {
    await mongoose.disconnect()
  }
}

// Run the appropriate script based on command line argument
const command = process.argv[2]

if (command === 'update') {
  updateAdminUser()
    .then(() => {
      logger.info('Admin user update completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('Admin user update failed:', error)
      process.exit(1)
    })
} else if (command === 'validate') {
  validateAdminUser()
    .then((isValid) => {
      logger.info('Admin user validation completed')
      process.exit(isValid ? 0 : 1)
    })
    .catch((error) => {
      logger.error('Admin user validation failed:', error)
      process.exit(1)
    })
} else {
  console.log('Usage:')
  console.log('  node src/scripts/updateAdminUser.js update    - Update admin user profile')
  console.log('  node src/scripts/updateAdminUser.js validate  - Validate admin user configuration')
  process.exit(1)
}
