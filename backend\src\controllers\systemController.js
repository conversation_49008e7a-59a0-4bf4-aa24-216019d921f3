import asyncHandler from 'express-async-handler'
import Candidate from '../models/Candidate.js'
import User from '../models/User.js'
import logger from '../utils/logger.js'

/**
 * @desc    Get all districts and municipalities dynamically from database
 * @route   GET /api/system/districts-municipalities
 * @access  Public
 */
export const getDistrictsAndMunicipalities = asyncHandler(async (req, res) => {
  try {
    // Get all unique districts and municipalities from candidates
    const candidateData = await Candidate.aggregate([
      {
        $group: {
          _id: '$district',
          municipalities: { $addToSet: '$municipalityName' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ])

    // Also get from users as backup
    const userData = await User.aggregate([
      {
        $group: {
          _id: '$district',
          municipalities: { $addToSet: '$municipality' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ])

    // Merge and deduplicate data
    const districtMap = {}
    const allDistricts = new Set()
    const allMunicipalities = new Set()

    // Process candidate data
    candidateData.forEach(item => {
      if (item._id) {
        allDistricts.add(item._id)
        if (!districtMap[item._id]) {
          districtMap[item._id] = new Set()
        }
        item.municipalities.forEach(municipality => {
          if (municipality) {
            districtMap[item._id].add(municipality)
            allMunicipalities.add(municipality)
          }
        })
      }
    })

    // Process user data as backup
    userData.forEach(item => {
      if (item._id) {
        allDistricts.add(item._id)
        if (!districtMap[item._id]) {
          districtMap[item._id] = new Set()
        }
        item.municipalities.forEach(municipality => {
          if (municipality) {
            districtMap[item._id].add(municipality)
            allMunicipalities.add(municipality)
          }
        })
      }
    })

    // Convert to arrays and sort
    const districts = Array.from(allDistricts).sort()
    const municipalities = Array.from(allMunicipalities).sort()
    
    // Convert district mapping to proper format
    const districtMapping = {}
    Object.keys(districtMap).forEach(district => {
      districtMapping[district] = Array.from(districtMap[district]).sort()
    })

    res.status(200).json({
      success: true,
      data: {
        districts,
        municipalities,
        districtMapping,
        totalDistricts: districts.length,
        totalMunicipalities: municipalities.length
      }
    })
  } catch (error) {
    logger.error('Error fetching districts and municipalities:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch districts and municipalities'
    })
  }
})

/**
 * @desc    Get system configuration and metadata
 * @route   GET /api/system/config
 * @access  Public
 */
export const getSystemConfig = asyncHandler(async (req, res) => {
  try {
    // Get counts from database
    const [candidateCount, userCount, districtCount] = await Promise.all([
      Candidate.countDocuments({ isActive: true }),
      User.countDocuments({ isActive: true }),
      Candidate.distinct('district').then(districts => districts.length)
    ])

    // Get available roles from User model
    const userRoles = ['voter', 'admin', 'execom', 'tie-breaker']

    res.status(200).json({
      success: true,
      data: {
        system: {
          name: 'DFPTA E-Voting System',
          version: '1.0.0',
          description: 'Department of Education - Camarines Sur PTA Election System'
        },
        statistics: {
          totalCandidates: candidateCount,
          totalUsers: userCount,
          totalDistricts: districtCount
        },
        configuration: {
          availableRoles: userRoles,
          maxCandidatesPerVote: 15, // This could be made dynamic later
          votingRounds: ['round1', 'tiebreaker1', 'tiebreaker2', 'tiebreaker3']
        }
      }
    })
  } catch (error) {
    logger.error('Error fetching system config:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system configuration'
    })
  }
})

/**
 * @desc    Get municipality details by name
 * @route   GET /api/system/municipality/:name
 * @access  Public
 */
export const getMunicipalityDetails = asyncHandler(async (req, res) => {
  const { name } = req.params

  try {
    // Get municipality from candidates
    const candidate = await Candidate.findOne({ 
      municipalityName: name,
      isActive: true 
    })

    if (!candidate) {
      return res.status(404).json({
        success: false,
        error: 'Municipality not found'
      })
    }

    // Get user count for this municipality
    const userCount = await User.countDocuments({ 
      municipality: name,
      isActive: true 
    })

    res.status(200).json({
      success: true,
      data: {
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        totalVotes: candidate.totalVotes,
        currentRank: candidate.currentRank,
        isActive: candidate.isActive,
        userCount,
        lastUpdated: candidate.updatedAt
      }
    })
  } catch (error) {
    logger.error('Error fetching municipality details:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch municipality details'
    })
  }
})

/**
 * @desc    Helper function to get district for municipality (dynamic)
 * @param   {string} municipalityName
 * @returns {Promise<string>} district
 */
export const getDistrictForMunicipality = async (municipalityName) => {
  try {
    const candidate = await Candidate.findOne({ 
      municipalityName,
      isActive: true 
    })
    
    if (candidate) {
      return candidate.district
    }

    // Fallback to user data
    const user = await User.findOne({ 
      municipality: municipalityName,
      isActive: true 
    })
    
    if (user) {
      return user.district
    }

    throw new Error(`District not found for municipality: ${municipalityName}`)
  } catch (error) {
    logger.error('Error getting district for municipality:', error)
    throw error
  }
}
