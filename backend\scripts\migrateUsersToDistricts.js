#!/usr/bin/env node

import mongoose from 'mongoose'
import dotenv from 'dotenv'
import User from '../src/models/User.js'
import logger from '../src/utils/logger.js'

// Load environment variables
dotenv.config()

// District mapping
const DISTRICT_MAPPING = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lupi', 'Ragay', 'Sipocot'],
  '2nd District': ['Gainza', 'Libmanan', 'Mi<PERSON>or', 'Minalabac', 'Pamplona', 'Pasacao', 'San Fernando'],
  '3rd District': ['Bombon', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': ['Caramoan', 'Garchitorena', 'Goa', 'Lagonoy', 'Parubcan', 'Sagnay', 'San Jose', 'Siruma', 'Tigaon', 'Tinambac'],
  '5th District': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Nabua']
}

// Function to get district for municipality
function getDistrictForMunicipality(municipality) {
  for (const [district, municipalities] of Object.entries(DISTRICT_MAPPING)) {
    if (municipalities.includes(municipality)) {
      return district
    }
  }
  throw new Error(`Municipality ${municipality} not found in any district`)
}

async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/dfpta_voting'
    await mongoose.connect(mongoURI)
    logger.info(`Connected to MongoDB: ${mongoURI}`)
  } catch (error) {
    logger.error('MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

async function migrateUsersToDistricts() {
  try {
    // Find all users without district field
    const usersWithoutDistrict = await User.find({
      $or: [
        { district: { $exists: false } },
        { district: null },
        { district: '' }
      ]
    })

    if (usersWithoutDistrict.length === 0) {
      logger.info('All users already have district information')
      return
    }

    logger.info(`Found ${usersWithoutDistrict.length} users without district information`)

    let updatedCount = 0
    let errorCount = 0

    for (const user of usersWithoutDistrict) {
      try {
        const district = getDistrictForMunicipality(user.municipality)
        
        await User.updateOne(
          { _id: user._id },
          { $set: { district: district } }
        )

        logger.info(`Updated user ${user.username} (${user.municipality}) -> ${district}`)
        updatedCount++
      } catch (error) {
        logger.error(`Error updating user ${user.username}: ${error.message}`)
        errorCount++
      }
    }

    logger.info(`Migration completed: ${updatedCount} users updated, ${errorCount} errors`)

    // Verify migration
    const usersStillWithoutDistrict = await User.countDocuments({
      $or: [
        { district: { $exists: false } },
        { district: null },
        { district: '' }
      ]
    })

    if (usersStillWithoutDistrict === 0) {
      logger.info('✅ Migration verification successful: All users have district information')
    } else {
      logger.warn(`⚠️ Migration incomplete: ${usersStillWithoutDistrict} users still without district`)
    }

    // Display summary by district
    const districtSummary = await User.aggregate([
      {
        $match: { isActive: true }
      },
      {
        $group: {
          _id: '$district',
          count: { $sum: 1 },
          roles: { $addToSet: '$role' },
          municipalities: { $addToSet: '$municipality' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ])

    logger.info('\n=== DISTRICT SUMMARY ===')
    districtSummary.forEach(district => {
      logger.info(`${district._id}: ${district.count} users, ${district.municipalities.length} municipalities`)
      logger.info(`  Roles: ${district.roles.join(', ')}`)
      logger.info(`  Municipalities: ${district.municipalities.sort().join(', ')}`)
    })
    logger.info('========================\n')

  } catch (error) {
    logger.error('Migration failed:', error.message)
    throw error
  }
}

async function validateDistrictMapping() {
  try {
    logger.info('Validating district mapping...')

    // Check for duplicate municipalities
    const allMunicipalities = Object.values(DISTRICT_MAPPING).flat()
    const uniqueMunicipalities = [...new Set(allMunicipalities)]
    
    if (allMunicipalities.length !== uniqueMunicipalities.length) {
      throw new Error('Duplicate municipalities found in district mapping')
    }

    // Check if all municipalities in database are covered
    const dbMunicipalities = await User.distinct('municipality')
    const unmappedMunicipalities = dbMunicipalities.filter(
      municipality => !allMunicipalities.includes(municipality)
    )

    if (unmappedMunicipalities.length > 0) {
      logger.warn(`Unmapped municipalities found: ${unmappedMunicipalities.join(', ')}`)
    }

    // Check for missing municipalities in database
    const missingMunicipalities = allMunicipalities.filter(
      municipality => !dbMunicipalities.includes(municipality)
    )

    if (missingMunicipalities.length > 0) {
      logger.warn(`Municipalities in mapping but not in database: ${missingMunicipalities.join(', ')}`)
    }

    logger.info('District mapping validation completed')
    logger.info(`Total municipalities in mapping: ${allMunicipalities.length}`)
    logger.info(`Total municipalities in database: ${dbMunicipalities.length}`)

  } catch (error) {
    logger.error('District mapping validation failed:', error.message)
    throw error
  }
}

async function main() {
  try {
    logger.info('Starting user district migration...')
    
    await connectDB()
    await validateDistrictMapping()
    await migrateUsersToDistricts()
    
    logger.info('User district migration completed successfully!')
    
  } catch (error) {
    logger.error('User district migration failed:', error.message)
    process.exit(1)
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Run the migration
main()
