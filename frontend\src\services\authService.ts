import { AuthResponse, LoginCredentials, User } from '../types'
import { apiClient } from './apiClient'

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/login', credentials)

    // Backend sends: { success: true, data: { user: {...}, token: "..." } }
    // Frontend expects: { success: true, user: {...}, token: "...", refreshToken: "..." }
    const backendData = response.data

    if (backendData.success && backendData.data) {
      return {
        success: true,
        user: backendData.data.user,
        token: backendData.data.token,
        refreshToken: backendData.data.refreshToken || '', // Backend doesn't send refresh token yet
      }
    } else {
      return {
        success: false,
        user: {} as User,
        token: '',
        refreshToken: '',
      }
    }
  },

  async logout(): Promise<void> {
    await apiClient.post('/auth/logout')
  },

  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get('/auth/me')
    return response.data.data
  },

  async refreshToken(): Promise<{ token: string; refreshToken: string }> {
    const refreshToken = localStorage.getItem('refreshToken')
    const response = await apiClient.post('/auth/refresh', { refreshToken })
    return response.data.data
  },
}
