<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start(); // Start a session
if (!isset($_SESSION['isTie'])) {
    $_SESSION['isTie'] = false;
}


// Function to check if the entered credentials are valid
function isValidCredentials($username, $password, $tieRegisteredAccounts) {
    return array_key_exists($username, $tieRegisteredAccounts) && $tieRegisteredAccounts[$username] === $password;
}

// Array of pre-registered accounts
$tieRegisteredAccounts = [
    "Baao" => "baao",
        "Balatan" => "balatan",
        "Bato" => "bato",
        "Bombon" => "bombon",
        "Buhi" => "buhi",
        "Bula" => "bula",
        "Cabusao" => "cabusao",
        "Calabanga" => "calabanga",
        "Camaligan" => "camaligan",
        "Canaman" => "canaman",
        "Caramoan" => "caramoan",
        "Del Gallego" => "delgallego",
        "Gainza" => "gainza",
        "Garchitorena" => "garchitorena",
        "Goa" => "goa",
        "Lagonoy" => "lagonoy",
        "Libmanan" => "libmanan",
        "Lupi" => "lupi",
        "Magarao" => "magarao",
        "Milaor" => "milaor",
        "Minalabac" => "minalabac",
        "Nabua" => "nabua",
        "Ocampo" => "ocampo",
        "Pamplona" => "pamplona",
        "Pasacao" => "pasacao",
        "Parubcan" => "parubcan",
        "Pili" => "pili",
        "Ragay" => "ragay",
        "Sagnay" => "sagnay",
        "San Fernando" => "sanfernando",
        "San Jose" => "sanjose",
        "Sipocot" => "sipocot",
        "Siruma" => "siruma",
        "Tigaon" => "tigaon",
        "Tinambac" => "tinambac"
];

// Check if the form is submitted
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Retrieve username and password from the login form
    $username = $_POST["username"];
    $password = $_POST["password"];

    // Check if the entered credentials match a pre-registered account
    if (isValidCredentials($username, $password, $tieRegisteredAccounts)) {
        // Authentication successful
        $tieuserMappings = [
            "Baao" => 1,
            "Balatan" => 2,
            "Bato" => 3,
            "Bombon" => 4,
            "Buhi" => 5,
            "Bula" => 6,
            "Cabusao" => 7,
            "Calabanga" => 8,
            "Camaligan" => 9,
            "Canaman" => 10,
            "Caramoan" => 11,
            "Del Gallego" => 12,
            "Gainza" => 13,
            "Garchitorena" => 14,
            "Goa" => 15,
            "Lagonoy" => 16,
            "Libmanan" => 17,
            "Lupi" => 18,
            "Magarao" => 19,
            "Milaor" => 20,
            "Minalabac" => 21,
            "Nabua" => 22,
            "Ocampo" => 23,
            "Pamplona" => 24,
            "Pasacao" => 25,
            "Parubcan" => 26,
            "Pili" => 27,
            "Ragay" => 28,
            "Sagnay" => 29,
            "San Fernando" => 30,
            "San Jose" => 31,
            "Sipocot" => 32,
            "Siruma" => 33,
            "Tigaon" => 34,
            "Tinambac" => 35
        ];

        $_SESSION['user_id'] = $tieuserMappings[$username]; // Use the mapped integer ID as the user identifier
        $_SESSION['voted'] = false; // Initialize the voted status as false
        header("Location: tie_vote.php"); // Redirect to the voting page
        exit;
    } else {
        // Invalid credentials, set an error message
        $errorMessage = 'Incorrect username or password. Please try again.';
    }
}

// Include the HTML template
include('tie_login.html');
?>
