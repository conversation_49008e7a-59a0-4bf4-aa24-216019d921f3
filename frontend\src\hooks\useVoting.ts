import { useCallback, useEffect, useMemo, useState } from 'react'
import { toast } from 'react-hot-toast'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import { votingService } from '../services/votingService'

// Candidates hooks
export function useCandidates() {
  return useQuery(['candidates'], () => votingService.getCandidates(), {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Voting status hooks
export function useVotingStatus() {
  return useQuery(['voting', 'status'], () => votingService.getVotingStatus(), {
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 10000, // Consider stale after 10 seconds
    retry: 3,
  })
}

// Public results hooks
export function usePublicResults() {
  return useQuery(['voting', 'public-results'], () => votingService.getPublicResults(), {
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000, // Consider stale after 30 seconds
    retry: 2,
  })
}

// Vote submission hook
export function useSubmitVotes() {
  const queryClient = useQueryClient()

  return useMutation((candidateIds: string[]) => votingService.submitVotes(candidateIds), {
    onSuccess: data => {
      // Invalidate and refetch voting status
      queryClient.invalidateQueries(['voting', 'status'])
      queryClient.invalidateQueries(['voting', 'history'])
      queryClient.invalidateQueries(['voting', 'results'])

      // Clear draft votes
      votingService.clearDraftVotes()

      toast.success(`Successfully submitted ${data.votesCount} votes!`)
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to submit votes'
      toast.error(message)
    },
  })
}

// Voting history hook
export function useVotingHistory() {
  return useQuery(['voting', 'history'], () => votingService.getVotingHistory(), {
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Results hooks
export function useVotingResults(round: number = 1, detailed: boolean = false) {
  return useQuery(
    ['voting', 'results', round, detailed],
    () => votingService.getResults(round, detailed),
    {
      refetchInterval: 30000, // Refresh every 30 seconds for live results
      staleTime: 15000, // Consider stale after 15 seconds
    }
  )
}

// Tie-breaker hooks
export function useActiveTieBreaker() {
  return useQuery(['tiebreaker', 'active'], () => votingService.getActiveTieBreaker(), {
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 10000, // Consider stale after 10 seconds
  })
}

export function useSubmitTieBreakerVote() {
  const queryClient = useQueryClient()

  return useMutation((candidateId: string) => votingService.submitTieBreakerVote(candidateId), {
    onSuccess: () => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries(['tiebreaker', 'active'])
      queryClient.invalidateQueries(['voting', 'status'])
      queryClient.invalidateQueries(['voting', 'results'])

      toast.success('Tie-breaker vote submitted successfully!')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to submit tie-breaker vote'
      toast.error(message)
    },
  })
}

export function useTieBreakerResults(tieBreakerID: string) {
  return useQuery(
    ['tiebreaker', 'results', tieBreakerID],
    () => votingService.getTieBreakerResults(tieBreakerID),
    {
      enabled: !!tieBreakerID,
      refetchInterval: 15000, // Refresh every 15 seconds for live results
    }
  )
}

// Custom hook for managing candidate selection
export function useCandidateSelection(maxSelections: number = 15) {
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([])
  const [isDirty, setIsDirty] = useState(false)

  // Load draft votes on mount
  useEffect(() => {
    const draftVotes = votingService.loadDraftVotes()
    if (draftVotes.length > 0) {
      setSelectedCandidates(draftVotes)
      setIsDirty(true)
    }
  }, [])

  // Save draft votes when selection changes
  useEffect(() => {
    if (isDirty) {
      votingService.saveDraftVotes(selectedCandidates)
    }
  }, [selectedCandidates, isDirty])

  const toggleCandidate = useCallback(
    (candidateId: string) => {
      setSelectedCandidates(prev => {
        const isSelected = prev.includes(candidateId)
        let newSelection: string[]

        if (isSelected) {
          // Remove candidate
          newSelection = prev.filter(id => id !== candidateId)
        } else {
          // Add candidate if under limit
          if (prev.length >= maxSelections) {
            toast.error(`You can only select up to ${maxSelections} candidates`)
            return prev
          }
          newSelection = [...prev, candidateId]
        }

        setIsDirty(true)
        return newSelection
      })
    },
    [maxSelections]
  )

  const selectAll = useCallback(
    (candidateIds: string[]) => {
      const limitedSelection = candidateIds.slice(0, maxSelections)
      setSelectedCandidates(limitedSelection)
      setIsDirty(true)

      if (candidateIds.length > maxSelections) {
        toast.error(`Only the first ${maxSelections} candidates were selected due to the limit`)
      }
    },
    [maxSelections]
  )

  const clearAll = useCallback(() => {
    setSelectedCandidates([])
    setIsDirty(true)
    votingService.clearDraftVotes()
  }, [])

  const isSelected = useCallback(
    (candidateId: string) => {
      return selectedCandidates.includes(candidateId)
    },
    [selectedCandidates]
  )

  const canSelectMore = selectedCandidates.length < maxSelections
  const remainingSelections = Math.max(0, maxSelections - selectedCandidates.length)

  const validation = votingService.validateCandidateSelection(selectedCandidates, maxSelections)

  return {
    selectedCandidates,
    selectedCount: selectedCandidates.length,
    maxSelections,
    remainingSelections,
    canSelectMore,
    isDirty,
    validation,
    toggleCandidate,
    selectAll,
    clearAll,
    isSelected,
  }
}

// Custom hook for voting eligibility
export function useVotingEligibility() {
  const { data: votingStatus, isLoading } = useVotingStatus()

  const eligibility = useMemo(() => {
    if (isLoading || !votingStatus) {
      return {
        canVote: false,
        reason: 'Loading...',
        isLoading: true,
      }
    }

    if (votingStatus.hasVoted) {
      return {
        canVote: false,
        reason: 'You have already voted. Thank you for your participation!',
        isLoading: false,
      }
    }

    if (votingStatus.votingStatus === 'no-active-session') {
      return {
        canVote: false,
        reason: 'No active voting session',
        isLoading: false,
      }
    }

    if (votingStatus.votingStatus === 'completed') {
      return {
        canVote: false,
        reason: 'Voting has been completed',
        isLoading: false,
      }
    }

    if (votingStatus.votingStatus === 'not-started') {
      return {
        canVote: false,
        reason: 'Voting has not started yet',
        isLoading: false,
      }
    }

    if (votingStatus.votingStatus === 'paused') {
      return {
        canVote: false,
        reason: 'Voting is currently paused',
        isLoading: false,
      }
    }

    if (votingStatus.votingStatus === 'tiebreaker') {
      if (
        votingStatus.tieBreakerStatus?.isActive &&
        votingStatus.tieBreakerStatus?.canParticipate
      ) {
        return {
          canVote: true,
          reason: 'Tie-breaker voting is active',
          isLoading: false,
          isTieBreaker: true,
        }
      } else {
        return {
          canVote: false,
          reason: 'Tie-breaker voting is not available for you',
          isLoading: false,
        }
      }
    }

    if (votingStatus.canVote) {
      return {
        canVote: true,
        reason: 'You are eligible to vote',
        isLoading: false,
      }
    }

    return {
      canVote: false,
      reason: 'Voting is not currently available',
      isLoading: false,
    }
  }, [votingStatus, isLoading])

  return eligibility
}

// Custom hook for real-time voting progress
export function useVotingProgress() {
  const { data: results } = useVotingResults()
  const { data: votingStatus } = useVotingStatus()

  const progress = useMemo(() => {
    if (!results || !votingStatus) return null

    const totalVoters = 35 // Total municipalities
    const participationRate = results.statistics.participationRate
    const votesSubmitted = results.statistics.uniqueVoters

    return {
      totalVoters,
      votesSubmitted,
      participationRate,
      remainingVoters: totalVoters - votesSubmitted,
      isComplete: participationRate === 100,
      status: votingStatus.votingStatus,
    }
  }, [results, votingStatus])

  return progress
}
