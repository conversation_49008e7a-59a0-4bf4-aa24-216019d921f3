import express from 'express'
import {
  getDistrictsAndMunicipalities,
  getMunicipalityDetails,
  getSystemConfig,
} from '../controllers/systemController.js'
// Optional auth middleware not needed for system endpoints

const router = express.Router()

/**
 * @swagger
 * /api/system/districts-municipalities:
 *   get:
 *     summary: Get all districts and municipalities dynamically
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Districts and municipalities retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     districts:
 *                       type: array
 *                       items:
 *                         type: string
 *                     municipalities:
 *                       type: array
 *                       items:
 *                         type: string
 *                     districtMapping:
 *                       type: object
 *                     totalDistricts:
 *                       type: number
 *                     totalMunicipalities:
 *                       type: number
 */
router.get('/districts-municipalities', getDistrictsAndMunicipalities)

/**
 * @swagger
 * /api/system/config:
 *   get:
 *     summary: Get system configuration and metadata
 *     tags: [System]
 *     responses:
 *       200:
 *         description: System configuration retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     system:
 *                       type: object
 *                     statistics:
 *                       type: object
 *                     configuration:
 *                       type: object
 */
router.get('/config', getSystemConfig)

/**
 * @swagger
 * /api/system/municipality/{name}:
 *   get:
 *     summary: Get municipality details by name
 *     tags: [System]
 *     parameters:
 *       - in: path
 *         name: name
 *         required: true
 *         schema:
 *           type: string
 *         description: Municipality name
 *     responses:
 *       200:
 *         description: Municipality details retrieved successfully
 *       404:
 *         description: Municipality not found
 */
router.get('/municipality/:name', getMunicipalityDetails)

export default router
