<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start the session if not already started
session_start();


// Start the session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if the top 15 candidates are stored in the session
if (!isset($_SESSION['top_15_candidates'])) {
    header('Location: admin_panel.php'); // Redirect to admin panel if top 15 candidates are not available
    exit;
}

// Retrieve the top 15 candidates from the session
$candidates = $_SESSION['top_15_candidates'];

// Sort candidates by total votes in descending order
usort($candidates, function ($a, $b) {
    return $b['total_votes'] - $a['total_votes'];
});

// Check if there is a tie for the 15th position
$minVotes15 = $candidates[14]['total_votes']; // Assuming the 15th position
$tieCandidates15 = array_filter($candidates, function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
});

// Check if there is a tie beyond the 15th position
$tieBeyond15 = count(array_filter(array_slice($candidates, 15), function ($candidate) use ($minVotes15) {
    return $candidate['total_votes'] == $minVotes15;
})) > 0;

// Check if there is a tie according to the specified conditions
$isTie = count($tieCandidates15) > 1 && $tieBeyond15;
$tiedCandidates = $isTie ? array_values($tieCandidates15) : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tie Verification</title>
    <!-- Uncomment the line below if you have a separate CSS file -->
    <!-- <link rel="stylesheet" type="text/css" href="style.css"> -->
</head>
<body>
    <header>
        <h1>Tie Verification</h1>
    </header>

    <section>
        <?php if ($isTie): ?>
            <div class="message">
                <p>There is a tie. The following candidates share the same vote value for the 15th Position:</p>
            </div>
            <table>
                <tr>
                    <th>No</th>
                    <th>Candidate Name</th>
                    <th>Total Votes</th>
                </tr>
                <?php foreach ($tiedCandidates as $count => $candidate): ?>
                    <tr>
                        <td><?= $count + 1 ?></td>
                        <td><?= $candidate['candidate_name'] ?></td>
                        <td><?= $candidate['total_votes'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
            <!-- Add a button to proceed to vote -->
            <form action="generated_list.php" method="get">
                <button type="submit">View the List</button>
            </form>
        <?php else: ?>
            <div class="message">
                <p>No tie. The top 15 most voted candidates can now log in <a href="execom_login.html">here</a>:</p>
            </div>
            <table>
                <tr>
                    <th>No</th>
                    <th>Candidate Name</th>
                    <th>Total Votes</th>
                </tr>
                <?php foreach (array_slice($candidates, 0, 15) as $count => $candidate): ?>
                    <tr>
                        <td><?= $count + 1 ?></td>
                        <td><?= $candidate['candidate_name'] ?></td>
                        <td><?= $candidate['total_votes'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </section>

    <footer>
        <div class="footer-row">
            <p>&copy; SOCMOB</p>
        </div>
    </footer>
</body>
</html>
