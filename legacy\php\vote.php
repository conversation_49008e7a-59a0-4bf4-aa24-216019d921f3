<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
include 'db_connection.php';

session_start();
$voter_id = $_SESSION['user_id']; // Get the user identifier from the session

// Check if the user has already voted
$check_sql = "SELECT * FROM votes WHERE voter_id = ?";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->bind_param("i", $voter_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows > 0) {
    echo "You have already cast your vote. You cannot vote again.<br><br>";
    echo '<a href="logout.php">Logout</a>'; // Add a logout link
} else {
    if (isset($_POST['candidate_id'])) {
        $candidate_ids = $_POST['candidate_id'];
        
        // Limit the number of selected candidates to a maximum of 15
        $candidate_ids = array_slice($candidate_ids, 0, 15);

        // Insert the new votes into the database using prepared statements
        $insert_sql = "INSERT INTO votes (voter_id, candidate_id) VALUES (?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);

        foreach ($candidate_ids as $candidate_id) {
            $insert_stmt->bind_param("ii", $voter_id, $candidate_id);
            $insert_stmt->execute();
        }

        $insert_stmt->close();

        // Successful vote, redirect to the vote panel page
        header("Location: vote_panel.php?voter_id=$voter_id");
        exit(); // Exit to prevent any additional output
    } else {
        // Handle the case where 'candidate_id' is not set
        echo "Candidate selection is required.";
    }
}

// Close the database connection
$conn->close();
?>
