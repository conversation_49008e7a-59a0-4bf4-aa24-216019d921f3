<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start(); // Start a session
include 'db_connection.php';

if ($_SERVER["REQUEST_METHOD"] === "POST") 
{
    // Retrieve username and password from the login form
    $username = $_POST["username"];
    $password = $_POST["password"];

    $userMappings = [
        "Baao" => 1,
        "Balatan" => 2,
        "Bato" => 3,
        "Bombon" => 4,
        "Buhi" => 5,
        "Bula" => 6,
        "Cabusao" => 7,
        "Calabanga" => 8,
        "Camaligan" => 9,
        "Canaman" => 10,
        "Caramoan" => 11,
        "<PERSON> Gallego" => 12,
        "Gainza" => 13,
        "Garchitorena" => 14,
        "Goa" => 15,
        "Lagonoy" => 16,
        "Libmanan" => 17,
        "Lupi" => 18,
        "Magarao" => 19,
        "<PERSON><PERSON>or" => 20,
        "Minalabac" => 21,
        "Nabu<PERSON>" => 22,
        "Ocampo" => 23,
        "<PERSON><PERSON><PERSON><PERSON>" => 24,
        "Pasaca<PERSON>" => 25,
        "<PERSON>rub<PERSON>" => 26,
        "<PERSON><PERSON>" => 27,
        "Ragay" => 28,
        "Sagnay" => 29,
        "San Fernando" => 30,
        "San Jose" => 31,
        "Sipocot" => 32,
        "Siruma" => 33,
        "Tigaon" => 34,
        "Tinambac" => 35
    ];

    // Define an array of pre-registered accounts
    $preRegisteredAccounts = [
        "Baao" => "b1top15",
        "Balatan" => "b2top15",
        "Bato" => "b3top15",
        "Bombon" => "b4top15",
        "Buhi" => "b5top15",
        "Bula" => "b6top15",
        "Cabusao" => "c1top15",
        "Calabanga" => "c2top15",
        "Camaligan" => "c3top15",
        "Canaman" => "c4top15",
        "Caramoan" => "c5top15",
        "Del Gallego" => "d1top15",
        "Gainza" => "g1top15",
        "Garchitorena" => "g2top15",
        "Goa" => "g3top15",
        "Lagonoy" => "l1top15",
        "Libmanan" => "l2top15",
        "Lupi" => "l3top15",
        "Magarao" => "m1top15",
        "Milaor" => "m2top15",
        "Minalabac" => "m3top15",
        "Nabua" => "n1top15",
        "Ocampo" => "o1top15",
        "Pamplona" => "p1top15",
        "Pasacao" => "p2top15",
        "Parubcan" => "p3top15",
        "Pili" => "p4top15",
        "Ragay" => "r1top15",
        "Sagnay" => "s1top15",
        "San Fernando" => "s2top15",
        "San Jose" => "s3top15",
        "Sipocot" => "s4top15",
        "Siruma" => "s5top15",
        "Tigaon" => "t1top15",
        "Tinambac" => "t2top15",
        // ... (other mappings)
    ];

    // Check if the entered credentials match a pre-registered account
    if (array_key_exists($username, $preRegisteredAccounts) && $preRegisteredAccounts[$username] === $password) {
        // Authentication successful
        $_SESSION['user_id'] = $userMappings[$username]; // Use the mapped integer ID as the user identifier

        // Check if the user is in the execom_members table
        $user_id = $_SESSION['user_id'];
        $check_execom_sql = "SELECT COUNT(*) as count FROM execom_members WHERE candidate_id = $user_id";
        $check_execom_result = $conn->query($check_execom_sql);

        if ($check_execom_result) {
            $row = $check_execom_result->fetch_assoc();
            $count = $row['count'];

            if ($count > 0) {
                // User is a member of execom, redirect to the voting page
                $_SESSION['voted'] = false; // Initialize the voted status as false
                header("Location: officerdashboard.php");
                exit();
            } else {
                // User is not a member of execom, show an error message
                $errorMessage = "Sorry, you cannot log in as an execom member.";
            }
        } else {
            // Error checking execom membership
            $errorMessage = "Error checking execom membership: " . $conn->error;
        }
    } else {
        // Invalid credentials, show an error message
        $errorMessage = 'Please check the credentials or you may ask the socmob staff for assistance. Thank you!';
    }
}

// Include the HTML template
include('execom_login.html');
?>